# ExperimentalModel Refactoring Documentation

## Overview

The `PreclinicalExperimentalModel` class has been refactored and renamed to `ExperimentalModel` to provide a streamlined, focused data model for capturing essential experimental model characteristics in ADC research while maintaining scientific accuracy and adding intelligent model name generation.

## Key Changes

### 1. Class Rename
- **Before**: `PreclinicalExperimentalModel`
- **After**: `ExperimentalModel`
- **Benefit**: More concise while maintaining clarity about purpose

### 2. Simplified Structure
- **Removed**: Complex optional fields (`culture_conditions`, `implantation_site`, `genetic_modifications`)
- **Removed**: Granular enums (`HostOrganismType`, `SourceMaterialType`)
- **Removed**: Complex validation methods for removed attributes
- **Benefit**: Focused on essential attributes without unnecessary complexity

### 3. Added ExperimentType as Primary Classification
- **New Field**: `experiment_type: ExperimentType` (required)
- **Values**: `IN_VITRO`, `IN_VIVO`, `EX_VIVO`
- **Purpose**: Primary classification determining experimental context and biological complexity

### 4. Standardized Model Classification
- **New Enum**: `ModelClassification` with standardized values
- **Field**: `model_classification: ModelClassification` (required)
- **Values**: Organized by experimental context (Cell Line, CDX, PDX, Organoid, etc.)
- **Benefit**: Ensures consistent terminology while maintaining enum type safety

### 5. Enhanced Model Name Handling
- **Enhanced Field**: `model_name` with intelligent auto-generation
- **Templates**: Standardized naming patterns based on model classification
- **Backward Compatibility**: Preserves explicit, descriptive model names
- **Benefit**: Creates consistent, informative model names when papers lack explicit names

### 6. Streamlined Validation

#### Single Validation Rule: Experiment-Model Consistency
- **IN_VITRO**: Only allows `CELL_LINE`, `NON_CELL_BASED`
- **EX_VIVO**: Only allows `ORGANOID`, `TISSUE_SPECIMENS`
- **IN_VIVO**: Only allows `CDX`, `PDX`, `SYNGENEIC`, `TRANSGENIC`, `RODENT_MODELS`, `NON_HUMAN_PRIMATES`

## Enums

### ExperimentType
```python
IN_VITRO = "In Vitro"      # Laboratory-based studies
IN_VIVO = "In Vivo"        # Whole organism studies
EX_VIVO = "Ex Vivo"        # Tissue-based studies outside organism
```

### ModelClassification
```python
# IN_VITRO models
CELL_LINE = "Cell Line"
NON_CELL_BASED = "Non-cell based"

# EX_VIVO models
ORGANOID = "Organoid"
TISSUE_SPECIMENS = "Tissue Specimens"

# IN_VIVO models
CDX = "CDX"
PDX = "PDX"
SYNGENEIC = "Syngeneic"
TRANSGENIC = "Transgenic"
RODENT_MODELS = "Rodent Models"
NON_HUMAN_PRIMATES = "Non-Human Primates"
```

## Benefits

1. **Simplicity**: Focused on essential attributes without unnecessary complexity
2. **Scientific Accuracy**: Maintains validation of core experiment-model relationships
3. **Type Safety**: Enum-based model classification ensures consistent terminology
4. **Clarity**: Clear hierarchical structure with primary experiment type classification
5. **Maintainability**: Reduced complexity makes the model easier to understand and maintain

## Model Name Enhancement Templates

The enhanced model name generation uses the following templates:

- **CDX Models**: `"{cell_line_name} CDX"` (e.g., "Calu-6 CDX") or `"{cancer_type} CDX"` for generic inputs
- **PDX Models**: `"{cancer_type} PDX"` (e.g., "Breast Cancer PDX")
- **Cell Line Models**: `"{cell_line_name} cells"` (e.g., "SKBR3 cells") or `"{cancer_type} cells"` for generic inputs
- **Organoid Models**: `"{cancer_type} organoids"` (e.g., "Lung Cancer organoids")
- **Other Models**: `"{cancer_type} {model_classification}"` (e.g., "Lung Cancer Syngeneic")

## Final Model Structure

The refactored `ExperimentalModel` now contains only essential fields:

- `citations: List[str]` - Source citations
- `experiment_type: ExperimentType` - Primary experimental context
- `model_name: str` - Model identifier with intelligent enhancement
- `model_classification: ModelClassification` - Standardized model type
- `cancer_type: str` - Primary cancer classification
- `cancer_subtype: Optional[str]` - Detailed cancer classification

## Migration Notes

- Update class name from `PreclinicalExperimentalModel` to `ExperimentalModel`
- Remove references to deleted fields (`host_organism_type`, `source_material_type`, `culture_conditions`, `implantation_site`, `genetic_modifications`)
- Update `model_classification` from string to `ModelClassification` enum
- Ensure all model instances use the new enum values
- Remove complex validation logic that referenced deleted attributes
- Benefit from automatic model name enhancement for incomplete names
