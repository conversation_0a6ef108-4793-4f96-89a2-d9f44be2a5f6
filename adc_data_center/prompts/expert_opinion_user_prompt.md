You are given the following:

ADC: The Antibody–Drug Conjugate (ADC) on which this endpoint measurement is performed:
{{ADC}}

ANTIGEN: The antigen targeted by the above ADC:
{{ANTIGEN}}

EXPERIMENT_TYPE: The type of experiment in which the endpoint measurement is performed:
{{EXPERIMENT_TYPE}}

EXPERIMENT_MODEL: The model experiment used for evaluating the measurements for the above ADC:
MODEL NAME: {{MODEL_NAME}}
MODEL TYPE: {{MODEL_TYPE}}

ENDPOINT NAME: The specific endpoint name that is being measured for the above ADC and Model:
{{ENDPOINT}}

ENDPOINT_MEASUREMENT: The exact measurement extracted from the research document:
{{ENDPOINT_MEASUREMENT}}

Your task is to carefully review both the measurement and its citation context. Judge whether the extraction of this measurement is legitimate according to the following strict requirements:

CRITICAL REQUIREMENTS
Approve a measurement only if it is explicitly, precisely, and directly reported for the given ADC and the specific experimental model (name and type) provided.
Do not approve measurements for a base or different experimental model if the citation only reports the value for a derived or related model, or vice versa.
Example: If the citation only gives an H-score for "Calu-6 cell-line–derived xenograft" or "tumor specimen" but not for the "Calu-6 cell line" itself, then do not approve the value as being for the "Calu-6 cell line" model.
Be alert to language such as “xenograft,” “tissue specimen,” or “tumor” which indicates data from animal or tissue-derived models, not the original base cell line. Measurements for such derived models must not be assigned to the base cell line model.
Similarly, if a measurement is only provided for a base/parent model (e.g., a cell line), do not approve its assignment to any derived or in vivo model unless the citation makes this explicit.
Only approve if the citation context makes it unequivocally clear that the value is for the exact experimental model provided.
For endpoint types:
Only approve ANTIGEN_EXPRESSION and ANTIGEN_EXPRESSION_H_SCORE measurements for the exact model as described above.
Only approve EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION for true EC50, not IC50 or other dose metrics.
Only approve HALF_LIFE_PERIOD if it is for the intact ADC, not its subcomponents.
Only approve LETHAL_DOSE if it is an actual lethal dose value (e.g., LD50) and not merely the highest safe or tested dose.
For ANTI_TUMOR_ACTIVITY_DOSE, only approve for in vivo animal models, not cell lines.

You get the endpoint names of following type:
- ANTIGEN_EXPRESSION: The level of antigen expression in the target cells. Make sure its measurements are specifically for the same experimental model and the antigent targetted by the ADC.
- ANTIGEN_EXPRESSION_H_SCORE: The H-score value for antigen expression. Make sure that this is a quantitative H-Score measurement only.
- EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION: Make sure that the captured measurement is infact the EC50 value and not any other dose response value, especially IC50.
- TUMOR_GROWTH_INHIBITION: The measure of the effectiveness of a treatment in inhibiting the growth of tumors. Assume that the effects cited on the cells / experimental model are tumor / cancer related only.
- CMAX_MAXIMUM_CONCENTRATION
- HALF_LIFE_PERIOD: Make sure you allow only half life measurements for Intact ADC not any sub component of ADC (Free Antibody, Free Payload etc...)
- INTERNALIZATION
- ANTI_TUMOR_ACTIVITY_DOSE: The dose at which anti-tumor activity is observed. Make sure that this is specifically for in vivo animal models and not in context of cell lines
- TOXICITY_FREQUENCY
- LETHAL_DOSE: The amount of ADC that causes death in a specified percentage of the experimental models (e.g., LD50, the dose lethal to 50% of the population). Especially take care that this is lethal dose, not the maximum dose administered / tested to be safe for this experimental model.

CRITICAL REQUIREMENT: Only approve the measurements that are specifically for the ADC name and the experimental model provided.

Output format
reasoning: Your reasoning for the judgement/opinion you provide.
opinion: A boolean value (true if the extraction is legitimate, false otherwise).

