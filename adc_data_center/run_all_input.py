import os
import glob
import subprocess

# Get all .md files in ./output/
md_files = glob.glob(os.path.join('../markdown_output', '*.md'))

if not md_files:
    print('No .md files found in ./output/')
    exit(0)

for md_file in md_files:
    print(f'Processing {md_file}...')
    if os.path.exists('output/' + md_file.split('\\')[-1].split('.')[0] + '_results.json'):
        print(f'Skipping {md_file}, results already exist.')
        continue
    cmd = [
        'python',
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        md_file
    ]
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f'Error processing {md_file}, exited with code {result.returncode}')
        break
print('Done.')
