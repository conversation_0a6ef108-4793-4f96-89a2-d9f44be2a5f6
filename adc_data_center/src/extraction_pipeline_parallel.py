import argparse
import pandas as pd
import asyncio
import os
import sqlite3
import logging
import json
from typing import List, Dict, Optional, Union, TypedDict, Annotated, Any, Tuple
from openai import AsyncAzureOpenAI
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from tqdm import tqdm
from dotenv import load_dotenv
from datetime import datetime
from pathlib import Path
from jinja2 import Template
from utils.extraction_pydantic_models import AntibodyDrugConjugate, Model, Endpoint, ModelWithEndpoints, ExperimentResult
from langgraph.graph import StateGraph, START, END
from langgraph.types import Send
from pydantic import BaseModel, Field
import operator
from operator import add, itemgetter
from dataclasses import dataclass, field


load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)



client = AsyncAzureOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    azure_deployment='gpt-4o',
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
)

# Initialize the OpenAI model with Azure provider
model = OpenAIModel(model_name='gpt-4o', provider=OpenAIProvider(openai_client=client))

# Initialize agents for each extraction step
adc_agent = Agent(
    model=model,
    system_prompt=open("prompts/adc_extraction_system_prompt.md").read(),
    result_type=List[AntibodyDrugConjugate],
    retries=3,
    result_retries=3,
    model_settings={'temperature': 0.0}
)

model_agent = Agent(
    model=model,
    system_prompt=open("prompts/model_extraction_system_prompt.md").read(),
    result_type=List[Model],
    retries=3,
    result_retries=3,
    model_settings={'temperature': 0.0}
)

endpoint_agent = Agent(
    model=model,
    system_prompt=open("prompts/endpoint_extraction_system_prompt.md").read(),
    result_type=List[Endpoint],
    retries=3,
    result_retries=3,
    model_settings={'temperature': 0.0}
)

# — redefine the graph state —
@dataclass(frozen=True)
class SharedContext:
    """Immutable core data for a sample that can be safely shared across parallel nodes.
    
    Attributes:
        raw_text (str): The raw text being processed
    """
    raw_text: str 
    
    def with_updates(self, **kwargs) -> "SharedContext":
        """Create a new instance with updated values."""
        return SharedContext(
            raw_text=kwargs.get('raw_text', self.raw_text)
        )

class ModelWithEndpoints(BaseModel):
    model: Model
    endpoints: Annotated[List[Endpoint], operator.add] = Field(default_factory=list)

class ADCExperiment(BaseModel):
    adc: AntibodyDrugConjugate
    models_with_endpoints: Annotated[List[ModelWithEndpoints], operator.add] = Field(default_factory=list)

class ExtractionState(TypedDict):
    shared_context: SharedContext 
    adcs: Annotated[List[AntibodyDrugConjugate], operator.add]
    # Append-only list of (adc_name, models_list)
       # Now holds full ADC object + its list of Model objects
    models: Annotated[
        List[Tuple[AntibodyDrugConjugate, List[Model]]],
        operator.add
    ]
    # Now holds full ADC object, Model object, + its list of Endpoints
    endpoints: Annotated[
        List[Tuple[AntibodyDrugConjugate, Model, List[Endpoint]]],
        operator.add
    ]
class ModelState(TypedDict):
    adc: AntibodyDrugConjugate
    raw_text: str

class EndpointState(TypedDict):
    adc: AntibodyDrugConjugate
    model: Model
    raw_text: str

def extract_adcs(state: ExtractionState) -> dict[str, List[AntibodyDrugConjugate]]:
    """Extract ADCs from the text"""
    logger.info("Extracting ADCs")
    user_prompt = Template(open("prompts/adc_extraction_user_prompt.md").read()).render(
        TEXT=state["shared_context"].raw_text
    )
    result = adc_agent.run_sync(user_prompt)
    
    # Ensure adcs is always a list
    adcs = result.data if isinstance(result.data, list) else [result.data]
    logger.info(f"Extracted {len(adcs)} ADCs")
    
    return {"adcs": adcs}


def extract_models(state: ModelState) -> dict[str, List[Tuple[AntibodyDrugConjugate, List[Model]]]]:
    """Extract models for a specific ADC"""
    adc = state["adc"]
    raw_text = state["raw_text"]
    
    logger.info(f"Extracting models for ADC: {adc.adc_name.standard_value}")
    user_prompt = Template(open("prompts/model_extraction_user_prompt.md").read()).render(
        TEXT=raw_text,
        ADC=adc.model_dump_json()
    )
    result = model_agent.run_sync(user_prompt)
    
    # Ensure models is always a list
    models = result.data if isinstance(result.data, list) else [result.data]
    logger.info(f"Extracted {len(models)} models for ADC: {adc.adc_name.standard_value}")
    
    return {"models": [(state["adc"], models)]}


def extract_endpoints(state: EndpointState) -> dict[str, List[Tuple[AntibodyDrugConjugate, Model, List[Endpoint]]]]:
    """Extract endpoints for a specific ADC-model pair"""
    adc = state["adc"]
    model = state["model"]
    raw_text = state["raw_text"]
    
    logger.info(f"Extracting endpoints for ADC: {adc.adc_name.standard_value}, Model: {model.model_type.standard_value}")
    user_prompt = Template(open("prompts/endpoint_extraction_user_prompt.md").read()).render(
        TEXT=raw_text,
        ADC=adc.model_dump_json(),
        MODEL=model.model_dump_json()
    )
    result = endpoint_agent.run_sync(user_prompt)
    
    # Get endpoints from result
    endpoints = result.data if isinstance(result.data, list) else [result.data]
    logger.info(f"Extracted {len(endpoints)} endpoints for ADC: {adc.adc_name.standard_value}, Model: {model.model_type.standard_value}")
    
    return {"endpoints": [(state["adc"], state["model"], endpoints)]}


def build_parallel_pipeline():
    """Build the parallel extraction pipeline"""
    logger.info("Building parallel extraction pipeline")
    graph = StateGraph(ExtractionState)

    # 1. Extract ADCs
    graph.add_node("extract_adcs", extract_adcs)
    graph.add_edge(START, "extract_adcs")
    
    # 2. Fan-out to extract_models for each ADC
    def fanout_models(state: ExtractionState):
        # Create a list of Send objects for each ADC
        return [
        Send("extract_models", {"adc": adc, "raw_text": state["shared_context"].raw_text})
        for adc in state["adcs"]
    ]
    
    graph.add_node("extract_models", extract_models)
    graph.add_conditional_edges("extract_adcs", fanout_models, ["extract_models"])

    # 3. Fan-out to extract_endpoints for each (ADC, Model) pair
    def fanout_endpoints(state: ExtractionState):
        sends: List[Send] = []
        for adc_obj, models in state["models"]:
            for model in models:
                sends.append(
                    Send(
                        "extract_endpoints",
                        {
                            "adc": adc_obj,
                            "model": model,
                            "raw_text": state["shared_context"].raw_text,
                        },
                    )
                )
        return sends
    
    graph.add_node("extract_endpoints", extract_endpoints)
    graph.add_conditional_edges("extract_models", fanout_endpoints, ["extract_endpoints"])
    graph.add_edge("extract_endpoints", END)

    logger.info("Parallel extraction pipeline built")
    return graph.compile()


def process_file_parallel(md_file, output_dir=None):
    """Process a markdown file using the parallel extraction pipeline"""
    logger.info(f"Processing file: {md_file}")
    
    # Read the input file
    with open(md_file, 'r', encoding='utf-8') as f:
        text = f.read()
    
    # Initialize state
    initial_state = {
        "shared_context": SharedContext(raw_text=text),
        "adcs": [],
        "models": [],
        "endpoints": []
    }
    
    # Build and run pipeline
    graph = build_parallel_pipeline()
    final_state = graph.invoke(initial_state)
    
    
    return final_state["endpoints"]
print(process_file_parallel('w2119870604.md'))

