import asyncio
import logging
import time
import re
from typing import Callable, TypeVar, Any, Optional, Dict
import aiohttp
import backoff
from tenacity import (
    retry,
    retry_if_exception_type,
    wait_exponential,
    wait_fixed,
    stop_after_attempt,
    RetryError,
    before_sleep_log
)

logger = logging.getLogger(__name__)

T = TypeVar('T')

class RateLimitError(Exception):
    """Exception raised when a rate limit is hit."""
    def __init__(self, message: str, retry_after: Optional[float] = None):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)

class OpenAIRateLimiter:
    """
    Rate limiter for OpenAI API calls that handles 429 errors and retries.
    
    This class provides decorators to handle rate limiting for both sync and async functions.
    """
    def __init__(
        self,
        min_retry_delay: float = 1.0,
        max_retry_delay: float = 60.0,
        default_retry_delay: float = 5.0,
        buffer_time: float = 1.0,
        max_concurrent: int = 5
    ):
        """
        Initialize the rate limiter.
        
        Args:
            min_retry_delay: Minimum delay between retries in seconds
            max_retry_delay: Maximum delay between retries in seconds
            default_retry_delay: Default delay if no retry-after header is provided
            buffer_time: Additional buffer time to add to the retry delay
            max_concurrent: Maximum number of concurrent requests
        """
        self.min_retry_delay = min_retry_delay
        self.max_retry_delay = max_retry_delay
        self.default_retry_delay = default_retry_delay
        self.buffer_time = buffer_time
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Track ongoing requests to avoid overwhelming the API
        self.ongoing_requests = 0
        self.lock = asyncio.Lock()
    
    def _extract_retry_after_from_message(self, message: str) -> Optional[float]:
        """Extract retry-after time from an error message."""
        # Common patterns in OpenAI error messages
        patterns = [
            r"Please retry after (\d+) seconds",
            r"Please try again in (\d+)s",
            r"Rate limit reached. Please wait (\d+) seconds",
            r"retry after (\d+\.?\d*) seconds",
            r"wait (\d+\.?\d*) seconds",
            r"available in (\d+\.?\d*) seconds"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                try:
                    return float(match.group(1))
                except (ValueError, TypeError):
                    pass
        
        return None
    
    def _extract_retry_after(self, error: Exception) -> float:
        """Extract retry-after time from an exception if available."""
        retry_after = None
        
        # Try to get retry-after from headers
        if hasattr(error, 'headers') and error.headers.get('retry-after'):
            try:
                retry_after = float(error.headers['retry-after'])
                logger.debug(f"Found retry-after in headers: {retry_after}s")
            except (ValueError, TypeError):
                pass
        
        # Try to get retry-after from the error object
        if retry_after is None and hasattr(error, 'retry_after') and error.retry_after is not None:
            retry_after = error.retry_after
            logger.debug(f"Found retry-after in error object: {retry_after}s")
        
        # Try to extract retry-after from the error message
        if retry_after is None:
            error_message = str(error)
            extracted = self._extract_retry_after_from_message(error_message)
            if extracted is not None:
                retry_after = extracted
                logger.debug(f"Extracted retry-after from message: {retry_after}s")
        
        # If we still don't have a retry time, use the default
        if retry_after is None:
            retry_after = self.default_retry_delay
            logger.debug(f"Using default retry delay: {retry_after}s")
        
        # Add buffer time to avoid hitting rate limits again
        retry_after += self.buffer_time
        logger.debug(f"Added buffer time, final retry delay: {retry_after}s")
        
        # Ensure the retry time is within bounds
        return max(self.min_retry_delay, min(retry_after, self.max_retry_delay))
    
    def _is_rate_limit_error(self, error: Exception) -> bool:
        """Check if an error is a rate limit error."""
        # Check for OpenAI's rate limit error
        if hasattr(error, 'status_code') and error.status_code == 429:
            return True
        
        # Check for our custom rate limit error
        if isinstance(error, RateLimitError):
            return True
        
        # Check for aiohttp ClientResponseError with 429 status
        if isinstance(error, aiohttp.ClientResponseError) and error.status == 429:
            return True
            
        # Check error message for rate limit indicators
        error_str = str(error).lower()
        rate_limit_phrases = [
            "rate limit", 
            "too many requests", 
            "capacity", 
            "quota exceeded",
            "try again",
            "please retry",
            "token rate limit"
        ]
        return any(phrase in error_str for phrase in rate_limit_phrases)
    
    async def _with_semaphore(self, func, *args, **kwargs):
        """Execute a function with a semaphore to limit concurrency."""
        async with self.semaphore:
            async with self.lock:
                self.ongoing_requests += 1
            try:
                return await func(*args, **kwargs)
            finally:
                async with self.lock:
                    self.ongoing_requests -= 1
    
    async def execute_with_retry(self, func: Callable[..., Any], *args, **kwargs) -> Any:
        """
        Execute a function with retry logic for rate limits.
        
        Args:
            func: The async function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of the function
        """
        retry_count = 0
        
        while True:  # Retry indefinitely until we get a non-rate-limit error or success
            try:
                return await self._with_semaphore(func, *args, **kwargs)
            except Exception as e:
                if not self._is_rate_limit_error(e):
                    # If it's not a rate limit error, re-raise
                    logger.error(f"Non-rate-limit error occurred: {str(e)}")
                    raise
                
                retry_count += 1
                retry_after = self._extract_retry_after(e)
                
                logger.warning(
                    f"Rate limit hit, retrying in {retry_after:.2f}s (attempt {retry_count})"
                )
                
                # Wait before retrying
                await asyncio.sleep(retry_after)
                logger.info(f"Retrying after waiting {retry_after:.2f}s")

    def async_rate_limited(self, func):
        """
        Decorator for async functions to handle rate limiting.
        
        Usage:
            @rate_limiter.async_rate_limited
            async def my_api_call():
                # Your API call here
        """
        async def wrapper(*args, **kwargs):
            return await self.execute_with_retry(func, *args, **kwargs)
        return wrapper
