import sys
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field, model_validator
from enum import Enum

# All the enum classes with NONE option
class AntigenExpressionLevels(str, Enum):
    HIGH = "High Antigen Expression"
    LOW = "Low Antigen Expression"
    MODERATE = "Moderate Antigen Expression"
    NONE = "Not Specified"

class ConcentrationComponents(str, Enum):
    INTACT_ADC = "Intact ADC"
    FREE_PAYLOAD = "Free Payload"
    FREE_ANTIBODY = "Free Antibody"
    OTHER = "Other Components"


class ExperimentType(str, Enum):
    """
    Primary classification of experimental contexts for ADC research.

    This enum defines the fundamental experimental environments where ADC studies
    are conducted, determining the biological complexity and clinical relevance
    of the experimental system.
    
    - IN_VITRO: Laboratory-based studies using cell cultures or biochemical systems
    - IN_VIVO: Whole organism studies using animal models
    - EX_VIVO: Tissue-based studies outside the living organism
    """
    # Laboratory-based studies using cell cultures or biochemical systems
    IN_VITRO = "In Vitro"

    # Whole organism studies using animal models
    IN_VIVO = "In Vivo"

    # Tissue-based studies outside the living organism
    EX_VIVO = "Ex Vivo"

class LinkerType(str, Enum):
    """
    Enumeration of linker types used in Antibody Drug Conjugates (ADCs).

    Linkers are chemical compounds that connect the antibody to the cytotoxic payload.
    The type of linker affects drug release mechanism and therapeutic efficacy.
    """
    # Linkers that can be cleaved inside target cells or sometimes in tumour microenvironment to release the payload
    CLEAVABLE = "Cleavable Linker"

    # Linkers that remain attached to the payload after internalization
    NON_CLEAVABLE = "Non-cleavable Linker"

    # Used when linker information is not available or not applicable
    NONE = "NONE"

class AntibodyClonality(str, Enum):
    """
    Enumeration of antibody clonality types.

    Clonality refers to whether the antibody is derived from a single clone
    or multiple clones of B cells, affecting specificity and production.
    """
    # Antibodies derived from a single clone of B cells, providing high specificity
    MONOCLONAL = "Monoclonal Antibody (mAb)"

    # Antibodies derived from multiple clones of B cells, providing broader reactivity
    POLYCLONAL = "Polyclonal Antibody (pAb)"

    # Used when clonality information is not available or not applicable
    NONE = "NONE"

class AntibodySpecies(str, Enum):
    """
    Enumeration of antibody species origin and engineering types.

    This classification indicates the source organism and level of humanization,
    which affects immunogenicity and therapeutic potential in humans.
    """
    # Antibodies derived from mice, may cause immunogenic reactions in humans
    MURINE = "Murine"

    # Antibodies with mouse variable regions and human constant regions
    CHIMERIC = "Chimeric"

    # Antibodies with human framework regions and mouse complementarity-determining regions
    HUMANIZED = "Humanized"

    # Used when species information is not available or not applicable
    NONE = "NONE"

class AntibodyIsotype(str, Enum):
    """
    Enumeration of antibody isotype classifications.

    Isotypes are classes of antibodies defined by their heavy chain constant regions,
    each with distinct biological functions and properties.
    """
    # Immunoglobulin G - most common therapeutic antibody class
    IGG = "IgG"

    # Immunoglobulin M - pentameric antibody, first responder in immune response
    IGM = "IgM"

    # Immunoglobulin A - primarily found in mucosal areas and secretions
    IGA = "IgA"

    # Immunoglobulin E - involved in allergic reactions and parasitic infections
    IGE = "IgE"

    # Immunoglobulin D - functions as antigen receptor on B cells
    IGD = "IgD"

    # Used when isotype information is not available or not applicable
    NONE = "NONE"

class EndpointType(str, Enum):
    SAFETY = "safety"
    EFFICACY = "efficacy"
    PHARMACOKINETICS = "pharmacokinetics"
    PHARMACODYNAMICS = "pharmacodynamics"
    BIOMARKER = "biomarker"
    NONE = "NONE"     

class AntibodyDrugConjugateType(str, Enum):
    """
    Enumeration of ADC types based on their role in research studies.

    This classification distinguishes between ADCs being tested versus
    those used as reference standards or comparators.
    """
    # ADCs that are the primary subject of investigation in the study
    INVESTIGATIVE = "Investigative"

    # ADCs used as benchmarks, controls, or approved comparators
    REFERENCE = "Reference"

class InVivoEndpointName(BaseModel):
    TUMOR_GROWTH_INHIBITION: bool = Field(..., description="Boolean value indicating The measure of the effectiveness of a treatment in inhibiting the growth of tumors explicitly described in the experimental model." 
    "TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group."
    "Look for quantification methods such as tumor volume measurements (e.g., caliper measurements, bioluminescence imaging, or MRI)")
    CMAX_MAXIMUM_CONCENTRATION: bool = Field(..., description="Boolean value indicating The highest concentration of the ADC (antibody + payload) in the blood after it is administered explicitly described in the experimental model."
    "Look for quantification methods such as serum or plasma concentration assays (e.g., ELISA) ")
    HALF_LIFE_PERIOD: bool = Field(..., description="Boolean value indicating The time it takes for the concentration of the ADC to reduce to half of its initial value after administration explicitly described in the experimental model."
    "Look for quantification methods such as serum or plasma concentration assays (e.g., ELISA) ")
    ANTI_TUMOR_ACTIVITY_DOSE: bool = Field(..., description="Boolean value indicating The amount of ADC or drug administered to the experimental model for anti-tumor activity assessment explicitly described in the experimental model.")
    TOXICITY_FREQUENCY: bool = Field(..., description="Boolean value indicating whether a dosing regimen (e.g., frequency, schedule) for ADC administration in toxicity evaluation is explicitly described in the experimental model.")
    LETHAL_DOSE: bool = Field(..., description="Boolean value indicating The amount of ADC that causes death in a specified percentage of the experimental models"
    "(e.g., True:LD50, the dose lethal to 50% of the population) explicitly described in the experimental model."
    "Look for quantification methods such as Dose-Mortality Response")

class InVitroEndpointName(BaseModel):
    ANTIGEN_EXPRESSION: bool = Field(..., description="Boolean value indicating The level of antigen expression in the target cells, categorized as high or low explicitly described in the experimental model. "
    "This categorization can be directly mentioned in the article or determined based on percentage thresholds, where more than 75% expression is considered high and less than 25% is considered low or any other calculations. "
    "Look for quantification methods such as Flow Cytometry or Western Blot.")
    ANTIGEN_EXPRESSION_H_SCORE: bool = Field(..., description="Boolean value indicating Quantitative measurement of antigen expression using H-score for ADC target validation explicitly described in the experimental model. It combines the intensity of staining and the percentage of positive cells to provide a comprehensive score ranging from 0 to 300."
    " Look for quantification methods such as Immunohistochemistry (IHC) or Immunocytochemistry (ICC).")
    EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION: bool = Field(..., description="Boolean value indicating The concentration of the drug that produces a response halfway between the baseline and maximum achievable response explicitly described in the experimental model."
    "This is often determined by dose-response curve analysis." 
    "Look for quantification methods such as non-cell-based dose-response assays (e.g., ELISA) or cell-based dose-response studies.")
    INTERNALIZATION: bool = Field(..., description="Boolean value indicating This metric quantifies the percentage of the total administered antibody-drug conjugate (ADC) that is successfully taken up by target cells within a specified time frame explicitly described in the experimental model."
    "It is calculated by comparing the amount of ADC internalized by the cells to the total amount of ADC initially available for binding and uptake."
    "Look for quantification methods such as flow cytometry, Confocal Microscopy, or Fluorescence Microscopy")
    
class ExVivoEndpointName(BaseModel):
    ANTIGEN_EXPRESSION_H_SCORE: bool = Field(..., description="Boolean value indicating Quantitative measurement of antigen expression using H-score for ADC target validation explicitly described in the experimental model. It combines the intensity of staining and the percentage of positive cells to provide a comprehensive score ranging from 0 to 300."
    "Look for quantification methods such as Immunohistochemistry (IHC) or Immunocytochemistry (ICC).")   

class EndpointName(BaseModel):
    ANTIGEN_EXPRESSION: bool = Field(..., description="Boolean value indicating The level of antigen expression in the target cells, categorized as high or low. This categorization can be directly mentioned in the article or determined based on percentage thresholds, where more than 75% expression is considered high and less than 25% is considered low or any other calculations. Look for quantification methods such as Flow Cytometry or Western Blot.")
    ANTIGEN_EXPRESSION_H_SCORE: bool = Field(..., description="Boolean value indicating Quantitative measurement of antigen expression using H-score for ADC target validation. It combines the intensity of staining and the percentage of positive cells to provide a comprehensive score ranging from 0 to 300."
    " Look for quantification methods such as Immunohistochemistry (IHC) or Immunocytochemistry (ICC).")
    EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION: bool = Field(..., description="Boolean value indicating The concentration of the drug that produces a response halfway between the baseline and maximum achievable response."
    "This is often determined by dose-response curve analysis." 
    "Look for quantification methods such as non-cell-based dose-response assays (e.g., ELISA) or cell-based dose-response studies.")
    TUMOR_GROWTH_INHIBITION: bool = Field(..., description="Boolean value indicating The measure of the effectiveness of a treatment in inhibiting the growth of tumors." 
    "TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group."
    "Look for quantification methods such as tumor volume measurements (e.g., caliper measurements, bioluminescence imaging, or MRI)")
    CMAX_MAXIMUM_CONCENTRATION: bool = Field(..., description="Boolean value indicating The highest concentration of the ADC (antibody + payload) in the blood after it is administered."
    "Look for quantification methods such as serum or plasma concentration assays (e.g., ELISA) ")
    HALF_LIFE_PERIOD: bool = Field(..., description="Boolean value indicating The time it takes for the concentration of the ADC to reduce to half of its initial value after administration."
    "Look for quantification methods such as serum or plasma concentration assays (e.g., ELISA) ")
    INTERNALIZATION: bool = Field(..., description="Boolean value indicating This metric quantifies the percentage of the total administered antibody-drug conjugate (ADC) that is successfully taken up by target cells within a specified time frame."
    "It is calculated by comparing the amount of ADC internalized by the cells to the total amount of ADC initially available for binding and uptake."
    "Look for quantification methods such as flow cytometry, Confocal Microscopy, or Fluorescence Microscopy")
    ANTI_TUMOR_ACTIVITY_DOSE: bool = Field(..., description="Boolean value indicating The amount of ADC or drug administered to the experimental model for anti-tumor activity assessment.")
    
    TOXICITY_FREQUENCY: bool = Field(..., description="Boolean value indicating The dosing regimen of the ADC administration for toxicity evaluation (e.g., once daily, twice weekly, Every 2 weeks for 4 weeks (3 times in total) etc.) in the experimental model.")

    LETHAL_DOSE: bool = Field(..., description="Boolean value indicating The amount of ADC that causes death in a specified percentage of the experimental models"
    "(e.g., True:LD50, the dose lethal to 50% of the population)."
    "Look for quantification methods such as Dose-Mortality Response") 
    
class Endpoint(BaseModel):
    """Base class for all endpoints"""
    measured_value: str = Field(None, description="The value of the endpoint")
    measured_time: str = Field(None, description="The timepoint at which the endpoint is measured")
    measured_concentration: str = Field(None, description="The concentration of the ADC at the timepoint of measurement")

class AntibodyDrugConjugate(BaseModel):
    """
    Comprehensive model for Antibody Drug Conjugate (ADC) information extraction.

    This model captures detailed information about ADCs including their components
    (antibody, linker, payload), targets, and supporting citations from research literature.

    An ADC consists of three main components:
    1. Antibody: Provides specificity for target antigen on tumour cells
    2. Linker: Chemical bridge connecting antibody to payload
    3. Payload: Cytotoxic drug that provides therapeutic effect by causing tumour cell death
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper containing the extracted information"
    )

    # ADC identification and classification
    adc_name: str = Field(
        ...,
        description="Official or common name of the Antibody Drug Conjugate. If the ADC does not have a specific name, then create one by combining the antibody name, linker name and payload name.",
    )

    adc_type: AntibodyDrugConjugateType = Field(
        ...,
        description="Classification of ADC role in the study: 'Investigative' for test subjects, 'Reference' for approved comparators or controls",
    )

    # Antibody component fields
    antibody_name: str = Field(
        ...,
        description="Name or identifier of the monoclonal antibody component",
    )

    antibody_clonality: AntibodyClonality = Field(
        ...,
        description="Clonality classification indicating whether derived from single (monoclonal) or multiple (polyclonal) B cell clones",
    )

    antibody_species: AntibodySpecies = Field(
        ...,
        description="Species origin and humanization status affecting immunogenicity profile",
    )

    antibody_isotype: AntibodyIsotype = Field(
        ...,
        description="Heavy chain class determining antibody structure and biological functions",
    )

    # Payload component fields
    payload_name: str = Field(
        ...,
        description="Chemical name or identifier of the cytotoxic drug payload",
    )

    payload_target: str = Field(
        ...,
        description="Specific molecular target, pathway, or mechanism of action for the cytotoxic payload",
    )

    # Linker component fields
    linker_name: str = Field(
        ...,
        description="Chemical name or identifier of the linker molecule connecting antibody to payload",
    )

    linker_type: LinkerType = Field(
        ...,
        description="Cleavage mechanism classification determining payload release strategy inside target cells",
    )

    # Target antigen fields
    antigen_name: str = Field(
        ...,
        description="Name or identifier of the cell surface antigen specifically recognized by the antibody component",
    )


class ModelClassification(str, Enum):
    """
    Enumeration of standardized experimental model classifications for ADC research.

    This classification provides standardized terminology for different types of
    experimental models used in preclinical ADC studies, organized by experimental context.

    - CELL_LINE: Cultured cells derived from a primary cell source
    - NON_CELL_BASED: In vitro systems not based on cultured cells such as biochemical assays, biophysical measurements, etc.
    - ORGANOID: Three-dimensional cell cultures resembling an organ's structure and function
    - TISSUE_SPECIMENS: Fresh or frozen tissue samples from patients or animals
    - CELL_DERIVED_XENOGRAFT: Cell line-derived xenografts grown in immunodeficient mice
    - PATIENT_DERIVED_XENOGRAFT: Patient-derived xenografts grown in immunodeficient mice
    - SYNGENEIC: Tumor cells from the same species as immunocompetent host
    - TRANSGENIC: Genetically engineered in vivo models designed to carry modified cancer related genes
    - RODENT_MODELS: Rodent models excluding CDX and PDX
    - NON_HUMAN_PRIMATES: Non-human primate models
    """
    # IN_VITRO models
    CELL_LINE = "Cell Line"
    NON_CELL_BASED = "Non-cell based"

    # EX_VIVO models
    ORGANOID = "Organoid"
    TISSUE_SPECIMENS = "Tissue Specimens"

    # IN_VIVO models
    CDX = "CDX"
    PDX = "PDX"
    SYNGENEIC = "Syngeneic"
    TRANSGENIC = "Transgenic"
    RODENT_MODELS = "Rodent Models"
    NON_HUMAN_PRIMATES = "Non-Human Primates"


class PreclinicalExperimentalModel(BaseModel):
    """
    Streamlined model for experimental systems used in ADC preclinical research and testing.

    This model captures essential information about experimental platforms used to evaluate Antibody Drug Conjugates (ADCs) in preclinical studies. It provides standardized classification and naming of experimental models while maintaining scientific accuracy through validation of experiment-model relationships.

    The model automatically generates descriptive names for experimental systems when
    research papers don't provide explicit model names, ensuring consistent and
    informative identification of experimental platforms.
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper describing the evaluated experimental model system used in the ADC study",
    )

    # Primary experimental classification
    experiment_type: ExperimentType = Field(
        ...,
        description="Primary classification of the experimental context determining biological complexity and clinical relevance",
    )

    # Model identification and classification
    model_name: str = Field(
        ...,
        description="Name or identifier of the experimental model system. Can be explicitly stated in the research paper or auto-generated using standardized templates when incomplete or generic names are provided",
    )

    model_classification: ModelClassification = Field(
        ...,
        description="Standardized classification of the experimental model type based on the experimental context",
    )

    # Cancer classification fields
    cancer_type: str = Field(
        ...,
        description="Primary cancer classification based on the organ or tissue of origin where the malignancy first developed",
    )

    cancer_subtype: Optional[str] = Field(
        None,
        description="Detailed cancer classification based on histological, molecular, or genetic characteristics that influence treatment response and prognosis",
    )

    @model_validator(mode='after')
    def validate_experiment_model_consistency(self):
        """Validate logical consistency between experiment_type and model_classification."""
        experiment_type = self.experiment_type
        model_classification = self.model_classification

        # Define valid combinations using enum values
        valid_combinations = {
            ExperimentType.IN_VITRO: {ModelClassification.CELL_LINE, ModelClassification.NON_CELL_BASED},
            ExperimentType.EX_VIVO: {ModelClassification.ORGANOID, ModelClassification.TISSUE_SPECIMENS},
            ExperimentType.IN_VIVO: {
                ModelClassification.CDX, ModelClassification.PDX, ModelClassification.SYNGENEIC,
                ModelClassification.TRANSGENIC, ModelClassification.RODENT_MODELS, ModelClassification.NON_HUMAN_PRIMATES
            }
        }

        if model_classification not in valid_combinations[experiment_type]:
            valid_options = [option.value for option in valid_combinations[experiment_type]]
            raise ValueError(
                f"Invalid combination: {experiment_type.value} experiments cannot use '{model_classification.value}' models. "
                f"Valid options for {experiment_type.value}: {', '.join(sorted(valid_options))}"
            )

        return self
# Specialized endpoint models named exactly after the EndpointName enum values
class AntigenExpression(BaseModel):
    """Endpoint Information about Antigen Expression for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: AntigenExpressionLevels = Field(None, description="Categorization of the level of antigen expression in the target cells, categorized as high, low, or moderate. If the categorization is not explicitly mentioned in the article, then it can be interpreted based on percentage of antigen expression. More than 75% expression is considered high, less than 25% is considered low, and between 25% and 75% is considered moderate.")

    
class AntigenExpressionHScore(BaseModel):
    """Endpoint measurement of antigen expression using H-score for ADC target validation with supporting citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="H-score value (0-300) representing semi-quantitative immunohistochemistry assessment of antigen expression intensity and distribution")


class EC50_HalfMaximalEffectiveConcentration(BaseModel):
    """Endpoint Information about a unique measurement of Half Maximal Effective Concentration (EC50) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of EC50")
    measured_value: str = Field(None, description="The concentration of the drug that produces a response halfway between the baseline and maximum achievable response. This is often determined by dose-response curve analysis and quantified using methods such as Non-cell based Dose-response(ELISA), Cell-based Dose-response. This value is typically expressed in nanomolar (nM) or micromolar (μM) units.")
    measured_time: str = Field(None, description="The specific time period after ADC administration that the EC50 value is measured.")

class TumorGrowthInhibition(BaseModel):
    """Endpoint Information about a unique measurement of Tumor Growth Inhibition (TGI) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of TGI")
    measured_value: str = Field(None, description="The measure of the effectiveness of a treatment in inhibiting the growth of tumors. TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group. It is usually quantified using methods such as Caliper measurement or Imaging techniques")
    measured_time: str = Field(None, description="The specific time period after ADC administration at which tumor volume is measure for assessing inhibition of tumor growth. This is typically expressed in days or weeks or months")
    measured_concentration: str = Field(None, description="The concentration of the ADC in the experimental model at the time of measurement. This is typically expressed in nanomolar (nM) or micromolar (μM) units.")

class Cmax_MaximumConcentration(BaseModel):
    """Endpoint Information about a unique measurement of Maximum Concentration (Cmax) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of Cmax")
    measured_value: str = Field(None, description="The highest concentration of the ADC in the blood after it has been administered. It is usually quantified using methods such as post-dose Serum sample analysis using appropriate techniques (e.g., ELISA) to determine the above component's concentration. This value is typically expressed in nanograms per milliliter (ng/mL) or micrograms per milliliter (μg/mL).")
    measured_time: str = Field(None, description="The specific time point/ time duration at which the peak plasma concentration (Cmax) of the above ADC is observed after administration. This is typically expressed in hours or days post-administration.")
    measured_concentration: str = Field(None, description="The dose of ADC administered to the experimental model, after which the maximum serum concentration (Cmax) is achieved. This is typically expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")

# TODO - Update endpoint checker system prompt - better descriptions of each endpoint

class HalfLifePeriod(BaseModel):
    """Endpoint Information about a unique measurement of Half Life Period for the given ADC component tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of Half Life Period")
    measured_value: str = Field(None, description="The time it takes for the concentration of the given ADC to reduce to half of its initial value after administration. This is usually quantified using methods such as post-dose Serum sample analysis using appropriate techniques (e.g., ELISA) to determine the above component's concentration. This value is typically expressed in hours or days.")
    measured_concentration: str = Field(None, description="The dose of ADC administered to the experimental model, after which the elimination half-life (t1/2) is observed. This is typically expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")

class Internalization(BaseModel):
    """Endpoint Information about a unique measurement of ADC internalization for the given ADC component tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted or interpreted for a single measurement of internalization")
    measured_value: str = Field(None, description="The percentage or amount of the administered ADC component that is internalized by target cells within a specified time frame. This is typically determined by comparing the amount detected to the total amount administered, using methods such as flow cytometry, confocal microscopy, or fluorescence microscopy. This value is usually expressed as a percentage or absolute amount.")
    measured_concentration: str = Field(None, description="The concentration or dose of ADC administered to the experimental model after which internalization is measured. This is typically expressed in nanomolar (nM), micrograms per milliliter (μg/mL), or milligrams per kilogram (mg/kg).")
    # Time elapsed since administration and capture of internalization
    measured_timepoint: str = Field(None, description="The time after ADC exposure when internalization was assessed, typically expressed in hours or days")

class AntiTumorActivityDose(BaseModel):
    """Endpoint Information about a unique measurement of effective ADC doses tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of effective dose")
    measured_value: str = Field(None, description="Single measurement of dose that is reported to be effective in anti tumor activity.")


# class ToxicityFrequency(BaseModel):
#     """Endpoint Information about a unique measurement of toxicity frequency tested on the experimental model for given ADC with citations"""
#     citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of toxicity frequency")
#     frequency: str = Field(None, description="Dosing schedule frequency (e.g., 'every 2 weeks')")
#     num_administrations: Optional[int] = Field(None, description="Total number of administrations (e.g., 3).")
#     measured_concentration: str = Field(None, description="Dose of ADC delivered per administration, typically in mg/kg (e.g., '80 mg/kg').")

class ToxicityFrequency(BaseModel):
    """Endpoint Information about a unique measurement of toxicity frequency tested on the experimental model for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of toxicity frequency")
    measured_value: str = Field(None, description="The dosing regimen of the ADC administration for toxicity evaluation (e.g. 'every 2 weeks for 4 weeks (3 times in total)') explicitly described in the experimental model.")
    measured_concentration: str = Field(None, description="The dose of drug (ADC) administered to the experimental model for toxicity evaluation, typically expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL). This is the amount of ADC given in each administration as part of the dosing regimen.")


class LethalDose(BaseModel):
    """Endpoint Information about a unique measurement of lethal dose tested on the experimental model for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of lethal dose")
    measured_value: str = Field(None, description="The amount of ADC that is shown to cause death in a specific percentage of experimental models (e.g., LD50, the dose lethal to 50% of the population). This is typically determined through dose-mortality response studies and is expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")
    measured_death_percentage: str = Field(None, description="The percentage of experimental models that succumbed to the lethal dose of ADC. This is typically expressed as a percentage (e.g., 50% for LD50).")

# Function to get the appropriate endpoint model class based on the endpoint name
def get_endpoint_model(endpoint_name: EndpointName) -> Type[Endpoint]:
    """Get the appropriate endpoint model class based on the endpoint name"""
    # Convert enum name to class name format (remove underscores and capitalize each word)
    # For example: ANTIGEN_EXPRESSION -> AntigenExpression
    class_name = ''.join(word.capitalize() for word in endpoint_name.split('_'))

    if class_name == 'Ec50HalfMaximalEffectiveConcentration':
        class_name = 'EC50_HalfMaximalEffectiveConcentration'
    
    if class_name == 'CmaxMaximumConcentration':
        class_name = 'Cmax_MaximumConcentration'
    
    # Get the module where this function is defined
    module = sys.modules[__name__]
    
    # Try to get the class by name from the module
    try:
        return getattr(module, class_name)
    except AttributeError:
        # If the class doesn't exist, return the base Endpoint class
        return Endpoint
