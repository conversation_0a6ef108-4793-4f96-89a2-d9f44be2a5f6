import sys
import json
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field, model_validator
from enum import Enum


# ---- Definition of Antibody Drug Conjugate (ADC) Components ----

class LinkerType(str, Enum):
    """
    Enumeration of linker types used in Antibody Drug Conjugates (ADCs).

    Linkers are chemical compounds that connect the antibody to the cytotoxic payload.
    The type of linker affects drug release mechanism and therapeutic efficacy.
    """
    # Linkers that can be cleaved inside target cells or sometimes in tumour microenvironment to release the payload
    CLEAVABLE = "Cleavable Linker"

    # Linkers that remain attached to the payload after internalization
    NON_CLEAVABLE = "Non-cleavable Linker"

    # Used when linker information is not available or not applicable
    NONE = "NONE"


class AntibodyClonality(str, Enum):
    """
    Enumeration of antibody clonality types.

    Clonality refers to whether the antibody is derived from a single clone
    or multiple clones of B cells, affecting specificity and production.
    """
    # Antibodies derived from a single clone of B cells, providing high specificity
    MONOCLONAL = "Monoclonal Antibody (mAb)"

    # Antibodies derived from multiple clones of B cells, providing broader reactivity
    POLYCLONAL = "Polyclonal Antibody (pAb)"

    # Used when clonality information is not available or not applicable
    NONE = "NONE"


class AntibodySpecies(str, Enum):
    """
    Enumeration of antibody species origin and engineering types.

    This classification indicates the source organism and level of humanization,
    which affects immunogenicity and therapeutic potential in humans.
    """
    # Antibodies derived from mice, may cause immunogenic reactions in humans
    MURINE = "Murine"

    # Antibodies with mouse variable regions and human constant regions
    CHIMERIC = "Chimeric"

    # Antibodies with human framework regions and mouse complementarity-determining regions
    HUMANIZED = "Humanized"

    # Used when species information is not available or not applicable
    NONE = "NONE"


class AntibodyIsotype(str, Enum):
    """
    Enumeration of antibody isotype classifications.

    Isotypes are classes of antibodies defined by their heavy chain constant regions,
    each with distinct biological functions and properties.
    """
    # Immunoglobulin G - most common therapeutic antibody class
    IGG = "IgG"

    # Immunoglobulin M - pentameric antibody, first responder in immune response
    IGM = "IgM"

    # Immunoglobulin A - primarily found in mucosal areas and secretions
    IGA = "IgA"

    # Immunoglobulin E - involved in allergic reactions and parasitic infections
    IGE = "IgE"

    # Immunoglobulin D - functions as antigen receptor on B cells
    IGD = "IgD"

    # Used when isotype information is not available or not applicable
    NONE = "NONE"


class AntibodyDrugConjugateType(str, Enum):
    """
    Enumeration of ADC types based on their role in research studies.

    This classification distinguishes between ADCs being tested versus
    those used as reference standards or comparators.
    """
    # ADCs that are the primary subject of investigation in the study
    INVESTIGATIVE = "Investigative"

    # ADCs used as benchmarks, controls, or approved comparators
    REFERENCE = "Reference"

class AntibodyDrugConjugate(BaseModel):
    """
    Comprehensive model for Antibody Drug Conjugate (ADC) information extraction.

    This model captures detailed information about ADCs including their components
    (antibody, linker, payload), targets, and supporting citations from research literature.

    An ADC consists of three main components:
    1. Antibody: Provides specificity for target antigen on tumour cells
    2. Linker: Chemical bridge connecting antibody to payload
    3. Payload: Cytotoxic drug that provides therapeutic effect by causing tumour cell death
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper containing the extracted information"
    )

    # ADC identification and classification
    adc_name: str = Field(
        ...,
        description="Official or common name of the Antibody Drug Conjugate. If the ADC does not have a specific name, then create one by combining the antibody name, linker name and payload name.",
    )

    adc_type: AntibodyDrugConjugateType = Field(
        ...,
        description="Classification of ADC role in the study: 'Investigative' for test subjects, 'Reference' for approved comparators or controls",
    )

    # Antibody component fields
    antibody_name: str = Field(
        ...,
        description="Name or identifier of the monoclonal antibody component",
    )

    antibody_clonality: AntibodyClonality = Field(
        ...,
        description="Clonality classification indicating whether derived from single (monoclonal) or multiple (polyclonal) B cell clones",
    )

    antibody_species: AntibodySpecies = Field(
        ...,
        description="Species origin and humanization status affecting immunogenicity profile",
    )

    antibody_isotype: AntibodyIsotype = Field(
        ...,
        description="Heavy chain class determining antibody structure and biological functions",
    )

    # Payload component fields
    payload_name: str = Field(
        ...,
        description="Chemical name or identifier of the cytotoxic drug payload",
    )

    payload_target: str = Field(
        ...,
        description="Specific molecular target, pathway, or mechanism of action for the cytotoxic payload",
    )

    # Linker component fields
    linker_name: str = Field(
        ...,
        description="Chemical name or identifier of the linker molecule connecting antibody to payload",
    )

    linker_type: LinkerType = Field(
        ...,
        description="Cleavage mechanism classification determining payload release strategy inside target cells",
    )

    # Target antigen fields
    antigen_name: str = Field(
        ...,
        description="Name or identifier of the cell surface antigen specifically recognized by the antibody component",
    )

# print("="*100)
# print(json.dumps(AntibodyDrugConjugate.model_json_schema(), indent=2))
# print("="*100)

# ---- Definition of Preclinical Experimental Models ----


class ExperimentType(str, Enum):
    """
    Primary classification of experimental contexts for ADC research.

    This enum defines the fundamental experimental environments where ADC studies
    are conducted, determining the biological complexity and clinical relevance
    of the experimental system.
    
    - IN_VITRO: Laboratory-based studies using cell cultures or biochemical systems
    - IN_VIVO: Whole organism studies using animal models
    - EX_VIVO: Tissue-based studies outside the living organism
    """
    # Laboratory-based studies using cell cultures or biochemical systems
    IN_VITRO = "In Vitro"

    # Whole organism studies using animal models
    IN_VIVO = "In Vivo"

    # Tissue-based studies outside the living organism
    EX_VIVO = "Ex Vivo"


class ModelClassification(str, Enum):
    """
    Enumeration of standardized experimental model classifications for ADC research.

    This classification provides standardized terminology for different types of
    experimental models used in preclinical ADC studies, organized by experimental context.

    - CELL_LINE: Cultured cells derived from a primary cell source
    - NON_CELL_BASED: In vitro systems not based on cultured cells such as biochemical assays, biophysical measurements, etc.
    - ORGANOID: Three-dimensional cell cultures resembling an organ's structure and function
    - TISSUE_SPECIMENS: Fresh or frozen tissue samples from patients or animals
    - CELL_DERIVED_XENOGRAFT: Cell line-derived xenografts grown in immunodeficient mice
    - PATIENT_DERIVED_XENOGRAFT: Patient-derived xenografts grown in immunodeficient mice
    - SYNGENEIC: Tumor cells from the same species as immunocompetent host
    - TRANSGENIC: Genetically engineered in vivo models designed to carry modified cancer related genes
    - RODENT_MODELS: Rodent models excluding CDX and PDX
    - NON_HUMAN_PRIMATES: Non-human primate models
    """
    # IN_VITRO models
    CELL_LINE = "Cell Line"
    NON_CELL_BASED = "Non-cell based"

    # EX_VIVO models
    ORGANOID = "Organoid"
    TISSUE_SPECIMENS = "Tissue Specimens"

    # IN_VIVO models
    CDX = "CDX"
    PDX = "PDX"
    SYNGENEIC = "Syngeneic"
    TRANSGENIC = "Transgenic"
    RODENT_MODELS = "Rodent Models"
    NON_HUMAN_PRIMATES = "Non-Human Primates"


class PreclinicalExperimentalModel(BaseModel):
    """
    Streamlined model for experimental systems used in ADC preclinical research and testing.

    This model captures essential information about experimental platforms used to evaluate Antibody Drug Conjugates (ADCs) in preclinical studies. It provides standardized classification and naming of experimental models while maintaining scientific accuracy through validation of experiment-model relationships.

    The model automatically generates descriptive names for experimental systems when
    research papers don't provide explicit model names, ensuring consistent and
    informative identification of experimental platforms.
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper describing the evaluated experimental model system used in the ADC study",
    )

    # Primary experimental classification
    experiment_type: ExperimentType = Field(
        ...,
        description="Primary classification of the experimental context determining biological complexity and clinical relevance",
    )

    # Model identification and classification
    model_name: str = Field(
        ...,
        description="Name or identifier of the experimental model system. Can be explicitly stated in the research paper or auto-generated using standardized templates when incomplete or generic names are provided",
    )

    model_classification: ModelClassification = Field(
        ...,
        description="Standardized classification of the experimental model type based on the experimental context",
    )

    # Cancer classification fields
    cancer_type: str = Field(
        ...,
        description="Primary cancer classification based on the organ or tissue of origin where the malignancy first developed",
    )

    cancer_subtype: Optional[str] = Field(
        None,
        description="Detailed cancer classification based on histological, molecular, or genetic characteristics that influence treatment response and prognosis",
    )

    @model_validator(mode='after')
    def validate_experiment_model_consistency(self):
        """Validate logical consistency between experiment_type and model_classification."""
        experiment_type = self.experiment_type
        model_classification = self.model_classification

        # Define valid combinations using enum values
        valid_combinations = {
            ExperimentType.IN_VITRO: {ModelClassification.CELL_LINE, ModelClassification.NON_CELL_BASED},
            ExperimentType.EX_VIVO: {ModelClassification.ORGANOID, ModelClassification.TISSUE_SPECIMENS},
            ExperimentType.IN_VIVO: {
                ModelClassification.CDX, ModelClassification.PDX, ModelClassification.SYNGENEIC,
                ModelClassification.TRANSGENIC, ModelClassification.RODENT_MODELS, ModelClassification.NON_HUMAN_PRIMATES
            }
        }

        if model_classification not in valid_combinations[experiment_type]:
            valid_options = [option.value for option in valid_combinations[experiment_type]]
            raise ValueError(
                f"Invalid combination: {experiment_type.value} experiments cannot use '{model_classification.value}' models. "
                f"Valid options for {experiment_type.value}: {', '.join(sorted(valid_options))}"
            )

        return self
