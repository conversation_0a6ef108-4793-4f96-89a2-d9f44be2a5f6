import pytest
import os
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import requests
from src.europe_pmc_downloader import (
    DownloadStatus,
    DatabaseHandler,
    SQLiteHandler,
    EuropePMCDownloader,
    PMCArticle,
    read_pmcids_from_openalex_db
)

@pytest.fixture
def temp_db_path(tmp_path):
    """Create a temporary database file"""
    db_path = tmp_path / "test.db"
    return str(db_path)

@pytest.fixture
def sqlite_handler(temp_db_path):
    """Create a SQLiteHandler instance with a temporary database"""
    return SQLiteHandler(temp_db_path)

@pytest.fixture
def mock_logger():
    """Create a mock logger"""
    logger = Mock()
    logger.info = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    return logger

@pytest.fixture
def mock_session():
    """Create a mock requests session"""
    session = Mock()
    session.get = Mock()
    return session

@pytest.fixture
def downloader(temp_db_path, mock_logger, mock_session):
    """Create a EuropePMCDownloader instance with mocked dependencies"""
    with patch('requests.Session', return_value=mock_session):
        downloader = EuropePMCDownloader(
            timeout=30,
            download_directory="test_downloads",
            db_path=temp_db_path,
            logger=mock_logger
        )
    return downloader

class TestSQLiteHandler:
    def test_init_db(self, sqlite_handler, temp_db_path):
        """Test database initialization"""
        # Verify tables were created
        with sqlite3.connect(temp_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            assert "dim_fulltext" in tables
            assert "dim_supplementary" in tables

    def test_update_fulltext_status(self, sqlite_handler):
        """Test updating fulltext status"""
        pmcid = "PMC123456"
        status = DownloadStatus.SUCCESS
        message = "Test message"
        download_path = "/test/path.xml"

        sqlite_handler.update_fulltext_status(pmcid, status, message, download_path)

        with sqlite3.connect(sqlite_handler.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT status, message, download_path FROM dim_fulltext WHERE pmcid = ?", (pmcid,))
            result = cursor.fetchone()
            assert result[0] == status.value
            assert result[1] == message
            assert result[2] == download_path

    def test_should_retry_fulltext(self, sqlite_handler):
        """Test retry logic for fulltext downloads"""
        pmcid = "PMC123456"
        
        # Test case: No previous attempt
        should_retry, reason = sqlite_handler.should_retry_fulltext(pmcid)
        assert should_retry
        assert reason is None

        # Test case: Success status
        sqlite_handler.update_fulltext_status(pmcid, DownloadStatus.SUCCESS, "Success")
        should_retry, reason = sqlite_handler.should_retry_fulltext(pmcid)
        assert not should_retry
        assert "already downloaded" in reason.lower()

        # Test case: Failed status with retries
        sqlite_handler.update_fulltext_status(pmcid, DownloadStatus.FAILED, "Failed")
        should_retry, reason = sqlite_handler.should_retry_fulltext(pmcid)
        assert should_retry

class TestEuropePMCDownloader:
    def test_download_fulltext_success(self, downloader, mock_session):
        """Test successful fulltext download"""
        pmcid = "PMC123456"
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<article>Test content</article>"
        mock_session.get.return_value = mock_response

        fulltext_path, message = downloader._download_fulltext(pmcid)

        assert fulltext_path is not None
        assert message is None
        assert fulltext_path.exists()
        assert fulltext_path.read_text() == mock_response.text
        mock_session.get.assert_called_once()

    def test_download_fulltext_not_available(self, downloader, mock_session):
        """Test fulltext download when article is not available"""
        pmcid = "PMC123456"
        mock_response = Mock()
        mock_response.status_code = 404
        mock_session.get.return_value = mock_response

        fulltext_path, message = downloader._download_fulltext(pmcid)

        assert fulltext_path is None
        assert "not available" in message.lower()
        mock_session.get.assert_called_once()

    def test_download_supplementary_success(self, downloader, mock_session):
        """Test successful supplementary files download"""
        pmcid = "PMC123456"
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b"Test zip content"
        mock_session.get.return_value = mock_response

        with patch('zipfile.ZipFile') as mock_zip:
            mock_zip.return_value.__enter__.return_value.namelist.return_value = ["file1.txt", "file2.txt"]
            has_suppl, message = downloader._download_supplementary(pmcid, Path("test_dir"))

        assert has_suppl
        assert message is None
        mock_session.get.assert_called_once()
        mock_zip.return_value.__enter__.return_value.extractall.assert_called_once()

    def test_download_articles(self, downloader, mock_session):
        """Test batch article download"""
        pmcids = ["PMC123456", "PMC789012"]
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<article>Test content</article>"
        mock_response.content = b"Test zip content"
        mock_session.get.return_value = mock_response

        with patch('zipfile.ZipFile') as mock_zip:
            mock_zip.return_value.__enter__.return_value.namelist.return_value = ["file1.txt"]
            articles = downloader.download_articles(pmcids, batch_size=2)

        assert len(articles) == 2
        assert all(isinstance(article, PMCArticle) for article in articles)
        assert all(article.has_fulltext for article in articles)
        assert all(article.has_suppl_files for article in articles)

    def test_retry_logic(self, downloader, mock_session):
        """Test retry logic with failed downloads"""
        pmcid = "PMC123456"
        
        # First attempt fails
        mock_response = Mock()
        mock_response.status_code = 500
        mock_session.get.return_value = mock_response

        fulltext_path, message = downloader._download_fulltext(pmcid)
        assert fulltext_path is None
        assert "error" in message.lower()

        # Second attempt succeeds
        mock_response.status_code = 200
        mock_response.text = "<article>Test content</article>"
        fulltext_path, message = downloader._download_fulltext(pmcid)
        assert fulltext_path is not None
        assert message is None

        # Verify retry count in database
        with sqlite3.connect(downloader.db_handler.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT retry_count FROM dim_fulltext WHERE pmcid = ?", (pmcid,))
            retry_count = cursor.fetchone()[0]
            assert retry_count == 2

    def test_read_pmcids_from_openalex_db(self, temp_db_path):
        """Test reading PMCIDs from OpenAlex database"""
        # Test case: Table doesn't exist
        with pytest.raises(sqlite3.OperationalError) as exc_info:
            read_pmcids_from_openalex_db(temp_db_path)
        assert "no such table: dim_openalex_works" in str(exc_info.value)

        # Test case: Table exists with valid data
        with sqlite3.connect(temp_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE dim_openalex_works (
                    ids TEXT,
                    pmcid TEXT
                )
            """)
            cursor.execute("""
                INSERT INTO dim_openalex_works (ids, pmcid)
                VALUES 
                    ('W1', 'PMC123456'),
                    ('W2', 'PMC789012')
            """)
            conn.commit()

        pmcids = read_pmcids_from_openalex_db(temp_db_path)
        assert len(pmcids) == 2
        assert "PMC123456" in pmcids
        assert "PMC789012" in pmcids

        # Test case: Table exists but empty
        with sqlite3.connect(temp_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM dim_openalex_works")
            conn.commit()

        pmcids = read_pmcids_from_openalex_db(temp_db_path)
        assert len(pmcids) == 0

if __name__ == "__main__":
    pytest.main([__file__]) 