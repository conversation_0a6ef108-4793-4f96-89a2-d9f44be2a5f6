#!/usr/bin/env python3
"""
Create comprehensive Excel analysis of XML extraction results
"""

import pandas as pd
import os
from pathlib import Path
import xml.etree.ElementTree as ET
import re
from collections import defaultdict

def analyze_xml_content(xml_file_path):
    """
    Analyze XML content to determine if it's full article or abstract only
    Returns: 'full_article', 'abstract_only', or 'no_content'
    """
    try:
        with open(xml_file_path, 'r', encoding='utf-8') as f:
            xml_content = f.read()
        
        # Parse XML
        root = ET.fromstring(xml_content)
        
        # Check for body content
        body_elements = root.findall('.//body')
        sec_elements = root.findall('.//sec')
        
        # Calculate content in body/sections
        body_content_length = 0
        
        for body in body_elements:
            body_text = ET.tostring(body, encoding='unicode', method='text')
            body_content_length += len(body_text.strip())
        
        for sec in sec_elements:
            sec_text = ET.tostring(sec, encoding='unicode', method='text')
            body_content_length += len(sec_text.strip())
        
        # Determine content type based on body content length
        if body_content_length > 1000:
            return 'full_article'
        elif body_content_length > 0:
            return 'abstract_only'
        else:
            # Check if there's at least an abstract
            abstract_elements = root.findall('.//abstract')
            if abstract_elements:
                return 'abstract_only'
            else:
                return 'no_content'
                
    except Exception as e:
        print(f"Error analyzing {xml_file_path}: {e}")
        return 'no_content'

def get_file_size_kb(file_path):
    """Get file size in kilobytes"""
    try:
        size_bytes = os.path.getsize(file_path)
        return round(size_bytes / 1024)
    except:
        return 0

def find_duplicate_files(work_id, directory):
    """
    Find all XML files for a given work ID including duplicates
    Returns: (count, list_of_filenames)
    """
    base_filename = f"{work_id}.xml"
    duplicate_pattern = f"{work_id}_*.xml"
    
    files_found = []
    directory_path = Path(directory)
    
    # Check for base file
    base_file = directory_path / base_filename
    if base_file.exists():
        files_found.append(base_filename)
    
    # Check for duplicate files
    for file_path in directory_path.glob(duplicate_pattern):
        files_found.append(file_path.name)
    
    # Sort files naturally (W123.xml, W123_2.xml, W123_3.xml, etc.)
    def natural_sort_key(filename):
        # Extract number after underscore, or 0 for base file
        match = re.search(r'_(\d+)\.xml$', filename)
        return int(match.group(1)) if match else 0
    
    files_found.sort(key=natural_sort_key)
    
    return len(files_found), files_found

def analyze_work_ids(work_ids, xml_directory, sheet_name):
    """
    Analyze work IDs and create analysis data
    """
    print(f"\n=== Analyzing {sheet_name} ===")
    
    analysis_data = []
    
    for i, work_id in enumerate(work_ids):
        if i % 50 == 0:
            print(f"Processing {i+1}/{len(work_ids)} work IDs...")
        
        # Find duplicate files
        duplicate_count, xml_filenames = find_duplicate_files(work_id, xml_directory)
        
        # Determine if XML is available
        xml_available = duplicate_count > 0
        
        # Analyze content type and file size
        if xml_available:
            # Use the first (main) file for analysis
            main_file_path = Path(xml_directory) / xml_filenames[0]
            content_type = analyze_xml_content(main_file_path)
            file_size_kb = get_file_size_kb(main_file_path)
            xml_filenames_str = ', '.join(xml_filenames)
        else:
            content_type = 'no_content'
            file_size_kb = 0
            xml_filenames_str = ''
        
        analysis_data.append({
            'oa_works_id': work_id,
            'xml_available': xml_available,
            'content_type': content_type,
            'duplicate_count': duplicate_count,
            'file_size_kb': file_size_kb,
            'xml_filenames': xml_filenames_str
        })
    
    return analysis_data

def create_summary_stats(analysis_data):
    """Create summary statistics for the analysis"""
    df = pd.DataFrame(analysis_data)
    
    total_ids = len(df)
    xml_available_count = df['xml_available'].sum()
    xml_missing_count = total_ids - xml_available_count
    
    content_type_counts = df['content_type'].value_counts()
    duplicate_stats = df[df['duplicate_count'] > 1]['duplicate_count'].describe()
    
    summary_stats = {
        'Total Work IDs': total_ids,
        'XML Available': xml_available_count,
        'XML Missing': xml_missing_count,
        'Success Rate (%)': round((xml_available_count / total_ids) * 100, 1),
        'Full Articles': content_type_counts.get('full_article', 0),
        'Abstract Only': content_type_counts.get('abstract_only', 0),
        'No Content': content_type_counts.get('no_content', 0),
        'IDs with Duplicates': len(df[df['duplicate_count'] > 1]),
        'Max Duplicates': df['duplicate_count'].max() if not df.empty else 0,
        'Avg File Size (KB)': round(df[df['file_size_kb'] > 0]['file_size_kb'].mean(), 1) if any(df['file_size_kb'] > 0) else 0
    }
    
    return summary_stats

def main():
    print("=== Creating XML Extraction Analysis ===")
    
    # Load original Excel file
    excel_file = 'scale_up_ids.xlsx'
    
    try:
        reviews_df = pd.read_excel(excel_file, sheet_name='review_ids')
        articles_df = pd.read_excel(excel_file, sheet_name='article_ids')
        
        print(f"✅ Loaded Excel file: {excel_file}")
        print(f"   Review IDs: {len(reviews_df)}")
        print(f"   Article IDs: {len(articles_df)}")
        
    except Exception as e:
        print(f"❌ Error loading Excel file: {e}")
        return
    
    # Extract work IDs
    review_ids = reviews_df['oa_works_id'].tolist()
    article_ids = articles_df['oa_works_id'].tolist()
    
    # Analyze reviews
    reviews_analysis = analyze_work_ids(review_ids, 'reviews', 'Reviews')
    reviews_stats = create_summary_stats(reviews_analysis)
    
    # Analyze articles  
    articles_analysis = analyze_work_ids(article_ids, 'articles', 'Articles')
    articles_stats = create_summary_stats(articles_analysis)
    
    # Create Excel file with analysis
    print("\n=== Creating Excel Analysis File ===")
    
    output_file = 'xml_extraction_analysis.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Create reviews analysis sheet
        reviews_df_analysis = pd.DataFrame(reviews_analysis)
        
        # Add summary stats as header rows
        summary_rows = []
        for key, value in reviews_stats.items():
            summary_rows.append({'oa_works_id': key, 'xml_available': value, 'content_type': '', 
                                'duplicate_count': '', 'file_size_kb': '', 'xml_filenames': ''})
        
        # Add separator row
        summary_rows.append({'oa_works_id': '--- DATA STARTS BELOW ---', 'xml_available': '', 
                           'content_type': '', 'duplicate_count': '', 'file_size_kb': '', 'xml_filenames': ''})
        
        # Combine summary and data
        summary_df = pd.DataFrame(summary_rows)
        final_reviews_df = pd.concat([summary_df, reviews_df_analysis], ignore_index=True)
        
        final_reviews_df.to_excel(writer, sheet_name='reviews_analysis', index=False)
        
        # Create articles analysis sheet
        articles_df_analysis = pd.DataFrame(articles_analysis)
        
        # Add summary stats as header rows
        summary_rows = []
        for key, value in articles_stats.items():
            summary_rows.append({'oa_works_id': key, 'xml_available': value, 'content_type': '', 
                                'duplicate_count': '', 'file_size_kb': '', 'xml_filenames': ''})
        
        # Add separator row
        summary_rows.append({'oa_works_id': '--- DATA STARTS BELOW ---', 'xml_available': '', 
                           'content_type': '', 'duplicate_count': '', 'file_size_kb': '', 'xml_filenames': ''})
        
        # Combine summary and data
        summary_df = pd.DataFrame(summary_rows)
        final_articles_df = pd.concat([summary_df, articles_df_analysis], ignore_index=True)
        
        final_articles_df.to_excel(writer, sheet_name='articles_analysis', index=False)
    
    print(f"✅ Created analysis file: {output_file}")
    
    # Print summary
    print("\n" + "="*60)
    print("📊 ANALYSIS SUMMARY")
    print("="*60)
    
    print(f"\n📋 REVIEWS ANALYSIS:")
    for key, value in reviews_stats.items():
        print(f"   {key}: {value}")
    
    print(f"\n📋 ARTICLES ANALYSIS:")
    for key, value in articles_stats.items():
        print(f"   {key}: {value}")
    
    print(f"\n✅ Analysis completed successfully!")
    print(f"   Output file: {output_file}")

if __name__ == "__main__":
    main()
