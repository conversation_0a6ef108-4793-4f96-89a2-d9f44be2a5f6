import pandas as pd
import sqlite3
import numpy as np
import dask.dataframe as dd

db_file = '/projects/ods/us_eds/users/ai_hub/tmp/openalex_data.db'
doc_parquet_file = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_doc_index'
sen_parquet_file = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_sen_index'

# define a class with df_input, df_doc, df_sen as variables and load_data method and add_bikg_data method
class BikgDataLoader:
    def __init__(self, db_file = db_file, doc_parquet_file = doc_parquet_file, sen_parquet_file = sen_parquet_file):
        self.db_file = db_file
        self.doc_parquet_file = doc_parquet_file
        self.sen_parquet_file = sen_parquet_file
        self.input = None
        self.df_doc = None
        self.df_sen = None


    def load_data(self, input_ids = None):
        """
        Load data from the database and parquet files. Used internally in add_bikg_data method.

        Args:
            input_ids (list, optional): List of input IDs. Example: ['https://openalex.org/W2802422632']
        """
        if input_ids is None:
            conn = sqlite3.connect(self.db_file)
            df_db = pd.read_sql_query("select * from dim_openalex_works;", conn)
            self.input = df_db['id'].tolist()
            conn.close()
        else:
            self.input = input_ids

        self.df_doc = dd.read_parquet(self.doc_parquet_file) #45M records
        self.df_sen = dd.read_parquet(self.sen_parquet_file) # 134M records

    def sort_and_merge_text(self, group):
        """
        Sorts the sentences in the right order within the group and merges them into a single string. Paragraphs delimited by \n.

        Args:
            group (pd.DataFrame): Group of rows to be sorted and merged.

        Returns:
            pd.DataFrame: DataFrame with merged text in the right order.
        """
        group = group.sort_values(by=['publication_par_i', 'publication_sen_i'], ascending=[True, True])
        # merge the new paragraph string with \n delimiter. 
        merged_text = []
        previous_par_i = None
        for _, row in group.iterrows():
            current_par_i = row['publication_par_i']
            if previous_par_i is not None and current_par_i > previous_par_i:
                # Add a newline before appending the text if the paragraph index increases
                merged_text.append('\n')
            # Append the current document text
            merged_text.append(row['document_text'])
            # Update the previous paragraph index
            previous_par_i = current_par_i

        return pd.DataFrame({
            'doc_hash': [group['doc_hash'].iloc[0]],
            'publication_section': [group['publication_section'].iloc[0]],
            'publication_sub_section': [group['publication_sub_section'].iloc[0]],
            'document_text': [''.join(merged_text)]
        })
    
    def pack_doc_sections(self, group):
        """
        Packs the document text into a str(list of dict) format where dict has the keys section, subSection, and text.
        Adds a content_size column that contains the size of the total doc text in chars.
        Adds a body_size column that contains the size of the body text in chars.

        Args:
            group (pd.DataFrame): Group of rows to be packed.

        Returns:
            pd.DataFrame: DataFrame with packed document sections.
        """
        return pd.DataFrame({
            'doc_hash': [group['doc_hash'].iloc[0]],
            'doc_sections': [group[['publication_section', 'publication_sub_section', 'document_text']]
                                .rename(columns={
                                    'publication_section': 'section',
                                    'publication_sub_section': 'subSection',
                                    'document_text': 'text'
                                }).to_dict('records')],
            'content_size': [group['document_text'].str.len().sum()],
            'body_size': [group[group['publication_section'] == 'body']['document_text'].str.len().sum()]
        })

    def add_bikg_data(self, input_ids = None):
        """
        Load BIKG data using provided openAlex IDs.

        Args:
        input_ids (list, optional): List of input IDs. Example: ['https://openalex.org/W2802422632']

        Returns:
            pd.DataFrame: DataFrame containing the merged data with additional columns.
        """
        self.load_data(input_ids)

        input = [id.split('/')[-1] for id in self.input]
        df_doc = self.df_doc[self.df_doc['oa_works_id'].isin(input)]

        # Filter df_sen for our input 134M to ~100K
        df_sen = self.df_sen[self.df_sen['doc_hash'].isin(df_doc['doc_hash'].compute())]

        # fill empty or NAN cells in sub_section as 'None' - to aid merging subsections under a section
        df_sen['publication_sub_section'] = df_sen['publication_sub_section'].replace('', np.nan).fillna('None')
        df_sen = df_sen.groupby(['doc_hash', 'publication_section', 'publication_sub_section']) \
                    .apply(
                        self.sort_and_merge_text, 
                    meta={
                            'doc_hash': 'string[pyarrow]', 
                            'publication_section': 'str', 
                            'publication_sub_section': 'str', 
                            'document_text':'str'}) \
                    .reset_index(drop=True)
    
        df_sen = df_sen.groupby('doc_hash') \
                    .apply(
                        self.pack_doc_sections,
                    meta ={'doc_hash': 'string[pyarrow]', 'doc_sections':'str', 'content_size': 'int64', 'body_size' : 'int64'}) \
                    .reset_index(drop=True)
        
        df_output = dd.from_pandas(pd.DataFrame({'oa_works_id': input}))
        df_output = df_output.merge(df_doc, on='oa_works_id', how='left')
        df_output = df_output.merge(df_sen, on='doc_hash', how='left')

        return df_output