{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ls ../zs_oa_adc_curated_annotated_with_content_2025.03.parquet/"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["df = pd.read_parquet('../zs_oa_adc_curated_annotated_with_content_2025.03.parquet/part-00001-tid-8614442468774910693-66f2681d-5f99-4681-a01a-9e4a48a13483-201817-1-c000.snappy.parquet')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>doc_hash</th>\n", "      <th>doc_provenance</th>\n", "      <th>doc_filename</th>\n", "      <th>extracted_ids</th>\n", "      <th>doc_sections</th>\n", "      <th>oa_works_id</th>\n", "      <th>doc_journal_names</th>\n", "      <th>doc_date</th>\n", "      <th>doc_types</th>\n", "      <th>doc_collectives</th>\n", "      <th>...</th>\n", "      <th>oa_works_summary_stats</th>\n", "      <th>oa_works_citation_normalized_percentile</th>\n", "      <th>oa_works_fwci</th>\n", "      <th>oa_works_mesh</th>\n", "      <th>oa_works_topics</th>\n", "      <th>oa_sources_issn_l</th>\n", "      <th>oa_sources_host_organization_name</th>\n", "      <th>oa_sources_summary_stats</th>\n", "      <th>n_duplicated_ids</th>\n", "      <th>doc_to_keep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>europepmc</td>\n", "      <td>PMC5400016_PMC5410000.xml.gz</td>\n", "      <td>[{'source': 'pmcid', 'id': '5408340'}, {'sourc...</td>\n", "      <td>[{'section': 'title', 'subSection': None, 'tex...</td>\n", "      <td>W2590433918</td>\n", "      <td>[ACS Cent Sci, ACS Cent Sci, oc, acscii]</td>\n", "      <td>2017-02-24</td>\n", "      <td>[research-article]</td>\n", "      <td>[]</td>\n", "      <td>...</td>\n", "      <td>{'2yr_cited_by_count': 28, 'cited_by_count': 134}</td>\n", "      <td>{'is_in_top_10_percent': True, 'is_in_top_1_pe...</td>\n", "      <td>5.887</td>\n", "      <td>[]</td>\n", "      <td>[{'display_name': 'Photochromic and Fluorescen...</td>\n", "      <td>2374-7943</td>\n", "      <td>American Chemical Society</td>\n", "      <td>{'2yr_cited_by_count': 43947, '2yr_h_index': 2...</td>\n", "      <td>[{'doc_hash': '10d273d2a16049acb36c1822e8ee5b2...</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 36 columns</p>\n", "</div>"], "text/plain": ["                                            doc_hash doc_provenance  \\\n", "0  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...      europepmc   \n", "\n", "                   doc_filename  \\\n", "0  PMC5400016_PMC5410000.xml.gz   \n", "\n", "                                       extracted_ids  \\\n", "0  [{'source': 'pmcid', 'id': '5408340'}, {'sourc...   \n", "\n", "                                        doc_sections  oa_works_id  \\\n", "0  [{'section': 'title', 'subSection': None, 'tex...  W2590433918   \n", "\n", "                          doc_journal_names    doc_date           doc_types  \\\n", "0  [ACS <PERSON>nt <PERSON>, ACS <PERSON>, oc, acscii]  2017-02-24  [research-article]   \n", "\n", "  doc_collectives  ...                             oa_works_summary_stats  \\\n", "0              []  ...  {'2yr_cited_by_count': 28, 'cited_by_count': 134}   \n", "\n", "             oa_works_citation_normalized_percentile oa_works_fwci  \\\n", "0  {'is_in_top_10_percent': True, 'is_in_top_1_pe...         5.887   \n", "\n", "   oa_works_mesh                                    oa_works_topics  \\\n", "0             []  [{'display_name': 'Photochromic and Fluorescen...   \n", "\n", "   oa_sources_issn_l oa_sources_host_organization_name  \\\n", "0          2374-7943         American Chemical Society   \n", "\n", "                            oa_sources_summary_stats  \\\n", "0  {'2yr_cited_by_count': 43947, '2yr_h_index': 2...   \n", "\n", "                                    n_duplicated_ids doc_to_keep  \n", "0  [{'doc_hash': '10d273d2a16049acb36c1822e8ee5b2...        True  \n", "\n", "[1 rows x 36 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df['doc_sections'][0]\n", "df[df['doc_hash'] == '10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9fe5789411578cb2a24']\n", "# df.info()\n", "# df.iloc[1]['doc_sections']"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df_latest = pd.read_parquet('/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_sen_index')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sentence_hash</th>\n", "      <th>document_text</th>\n", "      <th>doc_hash</th>\n", "      <th>publication_par_i</th>\n", "      <th>publication_sen_i</th>\n", "      <th>publication_sen_start</th>\n", "      <th>publication_sen_end</th>\n", "      <th>publication_section</th>\n", "      <th>publication_sub_section</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2199267</th>\n", "      <td>7023826100736967192</td>\n", "      <td>MDA-MB-468 cells were incubated with 100 pM o...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>255</td>\n", "      <td>1</td>\n", "      <td>30</td>\n", "      <td>91</td>\n", "      <td>body</td>\n", "      <td>in vitro and in vivo drugdelivery</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10221960</th>\n", "      <td>-7132392371037763139</td>\n", "      <td>and diethylamine-substituted heptamethine cyan...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>99</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>67</td>\n", "      <td>body</td>\n", "      <td>optimization of scaffold</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89689213</th>\n", "      <td>-2802607031383739058</td>\n", "      <td>Copper-catalyzed [3 + 2] cycloaddition with c...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>211</td>\n", "      <td>1</td>\n", "      <td>65</td>\n", "      <td>120</td>\n", "      <td>body</td>\n", "      <td>synthesis of cyaninecagedduocarmycin conjugates</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99683729</th>\n", "      <td>-8987393600943930033</td>\n", "      <td>a variety of tumor-associated antigens might b...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>358</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>56</td>\n", "      <td>body</td>\n", "      <td>conclusion</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                sentence_hash  \\\n", "2199267   7023826100736967192   \n", "10221960 -7132392371037763139   \n", "89689213 -2802607031383739058   \n", "99683729 -8987393600943930033   \n", "\n", "                                              document_text  \\\n", "2199267    MDA-MB-468 cells were incubated with 100 pM o...   \n", "10221960  and diethylamine-substituted heptamethine cyan...   \n", "89689213   Copper-catalyzed [3 + 2] cycloaddition with c...   \n", "99683729  a variety of tumor-associated antigens might b...   \n", "\n", "                                                   doc_hash  \\\n", "2199267   10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "10221960  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "89689213  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "99683729  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "\n", "          publication_par_i  publication_sen_i  publication_sen_start  \\\n", "2199267                 255                  1                     30   \n", "10221960                 99                  0                      0   \n", "89689213                211                  1                     65   \n", "99683729                358                  0                      0   \n", "\n", "          publication_sen_end publication_section  \\\n", "2199267                    91                body   \n", "10221960                   67                body   \n", "89689213                  120                body   \n", "99683729                   56                body   \n", "\n", "                                  publication_sub_section  \n", "2199267                 in vitro and in vivo drugdelivery  \n", "10221960                         optimization of scaffold  \n", "89689213  synthesis of cyaninecagedduocarmycin conjugates  \n", "99683729                                       conclusion  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_latest[df_latest['doc_hash'] == '10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9fe5789411578cb2a24']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loc1 = '/projects/it/public/bikg/downloads/2025-01-21-5.3.2/nlp_sen_index'\n", "loc2 = '/projects/it/public/bikg/downloads/2024-11-28-5.3.1/nlp_sen_index'\n", "loc3 = '/projects/it/public/bikg/downloads/2024-09-30-5.2.3/nlp_sen_index'\n", "loc4 = '/projects/it/public/bikg/downloads/2024-05-17-5.0.1/nlp_sen_index'\n", "\n", "df1 = pd.read_parquet(loc1)\n", "df2 = pd.read_parquet(loc2)\n", "df3 = pd.read_parquet(loc3)\n", "# df4 = pd.read_parquet(loc4)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import requests\n", "import shutil\n", "import io\n", "\n", "query = \"SELECT * from bikg.nlp_sen_index where doc_hash = '10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9fe5789411578cb2a24' FORMAT Parquet\"\n", "\n", "host = \"http://default:@clickhouse.bikg.astrazeneca.net:8123/\"\n", "with requests.post(url=host, data=query, stream=True) as r:\n", "    df_d = pd.read_parquet(io.BytesIO(r.raw.read()))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 0 files with column matching '*file*':\n"]}], "source": ["import os\n", "import pyarrow.parquet as pq\n", "import fnmatch\n", " \n", "def find_column_in_parquet_files(root_folder, column_pattern, case_insensitive=True):\n", "    matches = []\n", " \n", "    for dirpath, _, filenames in os.walk(root_folder):\n", "        for filename in filenames:\n", "            if filename.endswith(\".parquet\"):\n", "                file_path = os.path.join(dirpath, filename)\n", "                try:\n", "                    parquet_file = pq.ParquetFile(file_path)\n", "                    column_names = parquet_file.schema.names\n", " \n", "                    for col in column_names:\n", "                        col_check = col.lower() if case_insensitive else col\n", "                        pattern_check = column_pattern.lower() if case_insensitive else column_pattern\n", " \n", "                        if fnmatch.fnmatch(col_check, pattern_check):\n", "                            matches.append(file_path)\n", "                            break  # No need to check more columns in this file\n", "                except Exception as e:\n", "                    print(f\"Could not read {file_path}: {e}\")\n", " \n", "    return matches\n", " \n", "# Example usage\n", "folder_path = \"/projects/it/public/bikg/downloads/2025-04-03-5.4.4\"\n", "column_pattern = \"*file*\"  # wildcard pattern\n", "matching_files = find_column_in_parquet_files(folder_path, column_pattern)\n", " \n", "print(f\"Found {len(matching_files)} files with column matching '{column_pattern}':\")\n", "for file in matching_files:\n", "    print(file)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", " \n", "def find_value_in_parquet_files(root_folder, value_to_search, column_name=None):\n", "    matches = []\n", " \n", "    for dirpath, _, filenames in os.walk(root_folder):\n", "        for filename in filenames:\n", "            if filename.endswith(\".parquet\"):\n", "                file_path = os.path.join(dirpath, filename)\n", "                try:\n", "                    df = pd.read_parquet(file_path)\n", " \n", "                    if column_name:\n", "                        if column_name in df.columns and df[column_name].astype(str).str.contains(str(value_to_search), na=False).any():\n", "                            matches.append(file_path)\n", "                    else:\n", "                        # Search in all columns\n", "                        if df.astype(str).apply(lambda x: x.str.contains(str(value_to_search), na=False)).any().any():\n", "                            matches.append(file_path)\n", " \n", "                except Exception as e:\n", "                    print(f\"Could not read {file_path}: {e}\")\n", " \n", "    return matches\n", " \n", "# Example usage\n", "folder_path = \"/projects/it/public/bikg/downloads/2025-04-03-5.4.4\"\n", "value = \"in vitro and in vivo drugdelivery\"\n", "column = None  # or specify like \"user_id\"\n", " \n", "results = find_value_in_parquet_files(folder_path, value, column)\n", " \n", "print(f\"Found {len(results)} matching files:\")\n", "for file in results:\n", "    print(file)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['edge_id', 'source_id', 'source_type', 'source_label', 'target_id', 'target_type', 'target_label', 'relation', 'prov', 'element', 'evidence.aa_range', 'evidence.action', 'evidence.activity_id', 'evidence.activity_um', 'evidence.activity_value', 'evidence.activitycomment', 'evidence.adverse_event_occurences', 'evidence.adverse_event_weight', 'evidence.aim', 'element', 'evidence.annot_scenarios_tvcenrichment', 'evidence.annotation_strategy', 'evidence.assay_id', 'evidence.association_details', 'evidence.association_type', 'evidence.avg_evidence_score', 'evidence.avg_interaction_score', 'evidence.background_match_qty', 'evidence.background_total_qty', 'evidence.basemean', 'evidence.binding_site', 'evidence.binding_site_chembl_id', 'evidence.biomarkers', 'evidence.cell_line', 'evidence.cell_type', 'evidence.chi_squared', 'element', 'evidence.comment', 'element', 'element', 'evidence.confidence_level', 'evidence.corrected_p_value', 'evidence.count_class_alerts', 'evidence.count_clinical_alerts', 'evidence.count_drug_alerts', 'evidence.count_postmarketing_alerts', 'evidence.count_preclinical_alerts', 'evidence.count_target_discovery_preclinical_alerts', 'evidence.count_total_alerts', 'evidence.crispr_screen_name', 'evidence.cvp_bf', 'evidence.cvp_lfc', 'evidence.cvp_posfdr', 'element', 'evidence.datavaliditycomment', 'evidence.datavalidityissue', 'evidence.description', 'element', 'element', 'element', 'evidence.direction', 'element', 'evidence.diseasestageatspecimencollection', 'evidence.doc_date', 'evidence.doc_hash', 'evidence.doc_type', 'evidence.document', 'evidence.document_text', 'evidence.dosage_unit_name', 'evidence.drug_response', 'evidence.duration', 'evidence.dxatspecimenacquisition', 'evidence.dysfunction_pattern', 'evidence.edge_type', 'element', 'evidence.effect_details', 'evidence.effective_time_from_index', 'evidence.endpoint', 'evidence.enrichment_factor', 'element', 'element', 'evidence.experimental_system', 'evidence.experimental_system_type', 'evidence.expr_values', 'evidence.fisher_sum_p_value', 'evidence.found_in_mouse', 'evidence.gene_of_interest_status', 'evidence.graph_source', 'evidence.highest_go_level', 'evidence.hit_criteria', 'evidence.hit_name', 'evidence.hit_type_description', 'evidence.id_cp_interaction', 'evidence.indication_group', 'element', 'evidence.inference_chemical_qty', 'evidence.inference_gene_qty', 'evidence.inference_gene_symbol', 'element', 'evidence.inheritance_pattern', 'evidence.interaction', 'element', 'element', 'evidence.involvement', 'evidence.is_primary_drug', 'evidence.lfcse', 'evidence.library_methodology', 'evidence.library_name', 'evidence.link_id', 'evidence.literature_meta_data', 'evidence.llr_critical_vlaue', 'evidence.log10_min_qvalue', 'evidence.log2_fold_change', 'evidence.log2foldchange', 'evidence.log_likelihood_ratio', 'evidence.lot', 'evidence.mantis_ml_norm_score', 'evidence.mantis_ml_score', 'evidence.mapping_key', 'evidence.max_phase', 'evidence.mean_p_value', 'element', 'evidence.mechanism_id', 'evidence.medication_dosage_quantity', 'evidence.method', 'evidence.method_description', 'evidence.mirna_id', 'evidence.mirna_name', 'evidence.nc_rna_category', 'element', 'evidence.nsides_mean_reporting_frequency', 'evidence.nsides_no_side_effect_reports', 'evidence.nsides_prr', 'evidence.num_labels', 'evidence.offx_score', 'evidence.offx_score_number', 'evidence.omim_locus_id', 'evidence.on_ema_labels', 'evidence.on_fda_labels', 'evidence.on_mhra_labels', 'evidence.on_pmda_labels', 'evidence.only_metabolizing', 'evidence.organism', 'evidence.organism_id', 'evidence.organism_name_interactor_a', 'evidence.organism_name_interactor_b', 'evidence.p_value', 'element', 'evidence.padj', 'evidence.pchembl', 'evidence.percent_labels', 'evidence.phenotype', 'evidence.phosphosite', 'evidence.polyphen', 'evidence.potentialduplicate', 'evidence.premirna_id', 'evidence.probe_miner_score', 'evidence.probes_drugs_score', 'evidence.project_enzyme', 'evidence.project_name', 'evidence.project_screen_format', 'evidence.project_year', 'evidence.protein_intensity', 'evidence.protein_level', 'evidence.protein_variant_that_causes_se', 'element', 'element', 'element', 'evidence.ptm_details', 'evidence.pvalue', 'evidence.q_value', 'evidence.qval', 'evidence.rank_order', 'evidence.rank_order2', 'evidence.reference', 'element', 'evidence.reported_events_count', 'evidence.response', 'element', 'evidence.rna_level', 'evidence.rna_protein_coding_tpm', 'evidence.rna_raw_tpm', 'evidence.rna_tpm', 'evidence.rna_zscore', 'evidence.sample_type', 'evidence.score', 'evidence.score_1', 'evidence.score_2', 'element', 'evidence.screen_type', 'evidence.sentence_hash', 'evidence.sift', 'evidence.significance_indicator', 'evidence.significant_comparisons_ratio', 'evidence.signor_id', 'evidence.similarity', 'evidence.site_neighbourhood', 'element', 'element', 'evidence.source_end', 'element', 'evidence.source_start', 'evidence.source_type_description', 'evidence.source_type_name', 'evidence.specificdxatacquisition', 'evidence.standardrelation', 'evidence.standardtype', 'evidence.standardunits', 'evidence.standardvalue', 'evidence.status', 'evidence.subtypes', 'evidence.target_class_id', 'evidence.target_end', 'evidence.target_match_qty', 'element', 'evidence.target_start', 'evidence.target_tax_id', 'evidence.target_total_qty', 'evidence.target_type', 'evidence.targetstrength', 'evidence.throughput', 'evidence.tissue', 'evidence.total_copy_number', 'element', 'evidence.treatment_protocol', 'evidence.trust', 'evidence.trust_details', 'evidence.tvc_lfc', 'evidence.tvc_negfdr', 'evidence.tvc_negpval', 'evidence.tvc_posfdr', 'evidence.tvc_pospval', 'evidence.tvccvp_negativedesi', 'evidence.tvccvp_overalldesi', 'evidence.tvccvp_positivedesi', 'evidence.tvp_bf', 'evidence.tvp_lfc', 'evidence.type', 'evidence.unbiased', 'evidence.uniprot_source', 'evidence.units', 'evidence.url', 'evidence.urls', 'evidence.value', 'element', 'evidence.z_score']\n"]}], "source": ["df_doc = pd.read_parquet('/projects/it/public/bikg/downloads/2025-04-03-5.4.4/bikg_uncollapsed/part-00943-tid-1681293531219231403-2f834446-0f58-4143-97f8-07bc5dc65fb7-989143-1-c000.snappy.parquet')\n", "file_path = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/bikg_uncollapsed/part-00943-tid-1681293531219231403-2f834446-0f58-4143-97f8-07bc5dc65fb7-989143-1-c000.snappy.parquet'\n", "parquet_file = pq.ParquetFile(file_path)\n", "column_names = parquet_file.schema.names\n", "print(column_names)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sentence_hash</th>\n", "      <th>document_text</th>\n", "      <th>doc_hash</th>\n", "      <th>publication_par_i</th>\n", "      <th>publication_sen_i</th>\n", "      <th>publication_sen_start</th>\n", "      <th>publication_sen_end</th>\n", "      <th>publication_section</th>\n", "      <th>publication_sub_section</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2199267</th>\n", "      <td>7023826100736967192</td>\n", "      <td>MDA-MB-468 cells were incubated with 100 pM o...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>255</td>\n", "      <td>1</td>\n", "      <td>30</td>\n", "      <td>91</td>\n", "      <td>body</td>\n", "      <td>in vitro and in vivo drugdelivery</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10221960</th>\n", "      <td>-7132392371037763139</td>\n", "      <td>and diethylamine-substituted heptamethine cyan...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>99</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>67</td>\n", "      <td>body</td>\n", "      <td>optimization of scaffold</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89689213</th>\n", "      <td>-2802607031383739058</td>\n", "      <td>Copper-catalyzed [3 + 2] cycloaddition with c...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>211</td>\n", "      <td>1</td>\n", "      <td>65</td>\n", "      <td>120</td>\n", "      <td>body</td>\n", "      <td>synthesis of cyaninecagedduocarmycin conjugates</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99683729</th>\n", "      <td>-8987393600943930033</td>\n", "      <td>a variety of tumor-associated antigens might b...</td>\n", "      <td>10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...</td>\n", "      <td>358</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>56</td>\n", "      <td>body</td>\n", "      <td>conclusion</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                sentence_hash  \\\n", "2199267   7023826100736967192   \n", "10221960 -7132392371037763139   \n", "89689213 -2802607031383739058   \n", "99683729 -8987393600943930033   \n", "\n", "                                              document_text  \\\n", "2199267    MDA-MB-468 cells were incubated with 100 pM o...   \n", "10221960  and diethylamine-substituted heptamethine cyan...   \n", "89689213   Copper-catalyzed [3 + 2] cycloaddition with c...   \n", "99683729  a variety of tumor-associated antigens might b...   \n", "\n", "                                                   doc_hash  \\\n", "2199267   10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "10221960  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "89689213  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "99683729  10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9...   \n", "\n", "          publication_par_i  publication_sen_i  publication_sen_start  \\\n", "2199267                 255                  1                     30   \n", "10221960                 99                  0                      0   \n", "89689213                211                  1                     65   \n", "99683729                358                  0                      0   \n", "\n", "          publication_sen_end publication_section  \\\n", "2199267                    91                body   \n", "10221960                   67                body   \n", "89689213                  120                body   \n", "99683729                   56                body   \n", "\n", "                                  publication_sub_section  \n", "2199267                 in vitro and in vivo drugdelivery  \n", "10221960                         optimization of scaffold  \n", "89689213  synthesis of cyaninecagedduocarmycin conjugates  \n", "99683729                                       conclusion  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df_sen = pd.read_parquet('/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_sen_index')\n", "df_sen[df_sen['doc_hash'] == '10d273d2a16049acb36c1822e8ee5b281e748b16cc96c9fe5789411578cb2a24']"]}], "metadata": {"kernelspec": {"display_name": "ama_genai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}