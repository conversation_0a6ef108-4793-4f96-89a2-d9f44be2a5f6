{"cells": [{"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "import numpy as np\n", "from tqdm import tqdm\n", "import dask.dataframe as dd\n", "from dask.diagnostics import ProgressBar"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "import numpy as np\n", "import dask.dataframe as dd\n", "\n", "db_file = '/projects/ods/us_eds/users/ai_hub/tmp/openalex_data.db'\n", "doc_parquet_file = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_doc_index'\n", "sen_parquet_file = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_sen_index'\n", "\n", "# define a class with df_input, df_doc, df_sen as variables and load_data method and add_bikg_data method\n", "class BikgDataLoader:\n", "    def __init__(self, db_file = db_file, doc_parquet_file = doc_parquet_file, sen_parquet_file = sen_parquet_file):\n", "        self.db_file = db_file\n", "        self.doc_parquet_file = doc_parquet_file\n", "        self.sen_parquet_file = sen_parquet_file\n", "        self.input = None\n", "        self.df_doc = None\n", "        self.df_sen = None\n", "\n", "    \"\"\"\n", "    Load data from the database and parquet files. Used internally in add_bikg_data method.\n", "\n", "    Args:\n", "        input_ids (list, optional): List of input IDs. Example: ['https://openalex.org/W2802422632']\n", "    \"\"\"\n", "    def load_data(self, input_ids = None):\n", "        if input_ids is None:\n", "            conn = sqlite3.connect(self.db_file)\n", "            df_db = pd.read_sql_query(\"select * from dim_openalex_works;\", conn)\n", "            self.input = df_db['id'].tolist()\n", "            conn.close()\n", "        else:\n", "            self.input = input_ids\n", "\n", "        self.df_doc = dd.read_parquet(self.doc_parquet_file) #45M records\n", "        self.df_sen = dd.read_parquet(self.sen_parquet_file) # 134M records\n", "\n", "    def sort_and_merge_text(self, group):\n", "        \"\"\"\n", "        Sorts the sentences in the right order within the group and merges them into a single string. Paragraphs delimited by \\n.\n", "\n", "        Args:\n", "            group (pd.DataFrame): Group of rows to be sorted and merged.\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame with merged text in the right order.\n", "        \"\"\"\n", "        group = group.sort_values(by=['publication_par_i', 'publication_sen_i'], ascending=[True, True])\n", "        # merge the new paragraph string with \\n delimiter. \n", "        merged_text = []\n", "        previous_par_i = None\n", "        for _, row in group.iterrows():\n", "            current_par_i = row['publication_par_i']\n", "            if previous_par_i is not None and current_par_i > previous_par_i:\n", "                # Add a newline before appending the text if the paragraph index increases\n", "                merged_text.append('\\n')\n", "            # Append the current document text\n", "            merged_text.append(row['document_text'])\n", "            # Update the previous paragraph index\n", "            previous_par_i = current_par_i\n", "\n", "        return pd.DataFrame({\n", "            'doc_hash': [group['doc_hash'].iloc[0]],\n", "            'publication_section': [group['publication_section'].iloc[0]],\n", "            'publication_sub_section': [group['publication_sub_section'].iloc[0]],\n", "            'document_text': [''.join(merged_text)]\n", "        })\n", "    \n", "    def pack_doc_sections(self, group):\n", "        \"\"\"\n", "        Packs the document text into a str(list of dict) format where dict has the keys section, subSection, and text.\n", "        Adds a content_size column that contains the size of the total doc text in chars.\n", "        Adds a body_size column that contains the size of the body text in chars.\n", "\n", "        Args:\n", "            group (pd.DataFrame): Group of rows to be packed.\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame with packed document sections.\n", "        \"\"\"\n", "        return pd.DataFrame({\n", "            'doc_hash': [group['doc_hash'].iloc[0]],\n", "            'doc_sections': [group[['publication_section', 'publication_sub_section', 'document_text']]\n", "                                .rename(columns={\n", "                                    'publication_section': 'section',\n", "                                    'publication_sub_section': 'subSection',\n", "                                    'document_text': 'text'\n", "                                }).to_dict('records')],\n", "            'content_size': [group['document_text'].str.len().sum()],\n", "            'body_size': [group[group['publication_section'] == 'body']['document_text'].str.len().sum()]\n", "        })\n", "\n", "    def add_bikg_data(self, input_ids = None):\n", "        \"\"\"\n", "        Load BIKG data using provided openAlex IDs.\n", "\n", "        Args:\n", "        input_ids (list, optional): List of input IDs. Example: ['https://openalex.org/W2802422632']\n", "\n", "        Returns:\n", "            pd.DataFrame: DataFrame containing the merged data with additional columns.\n", "        \"\"\"\n", "        self.load_data(input_ids)\n", "\n", "        input = [id.split('/')[-1] for id in self.input]\n", "        df_output = self.df_doc[self.df_doc['oa_works_id'].isin(input)]\n", "\n", "        # The next step of filtering requires df_output computed beforehand\n", "        # df_output = df_output.persist()\n", "        # Filter df_sen for our input 134M to ~100K\n", "        df_sen = self.df_sen[self.df_sen['doc_hash'].isin(df_output['doc_hash'].compute())]\n", "\n", "        # fill empty or NAN cells in sub_section as 'None' - to aid merging subsections under a section\n", "        df_sen['publication_sub_section'] = df_sen['publication_sub_section'].replace('', np.nan).fillna('None')\n", "        df_sen = df_sen.groupby(['doc_hash', 'publication_section', 'publication_sub_section']) \\\n", "                    .apply(\n", "                        self.sort_and_merge_text, \n", "                    meta={\n", "                            'doc_hash': 'string[pyarrow]', \n", "                            'publication_section': 'str', \n", "                            'publication_sub_section': 'str', \n", "                            'document_text':'str'}) \\\n", "                    .reset_index(drop=True)\n", "    \n", "        df_sen = df_sen.groupby('doc_hash') \\\n", "                    .apply(\n", "                        self.pack_doc_sections,\n", "                    meta ={'doc_hash': 'string[pyarrow]', 'doc_sections':'str', 'content_size': 'int64', 'body_size' : 'int64'}) \\\n", "                    .reset_index(drop=True)\n", "        \n", "        df_output = df_output.merge(df_sen, on='doc_hash', how='left')\n", "\n", "        return df_output"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[##                                      ] | 7% Completed | 2.57 s ms"]}, {"name": "stdout", "output_type": "stream", "text": ["[########################################] | 100% Completed | 4.54 s\n", "[                                        ] | 2% Completed | 17.33 sms\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[50]\u001b[39m\u001b[32m, line 6\u001b[39m\n\u001b[32m      4\u001b[39m op = loader.add_bikg_data()\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m#25299\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m6\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mop\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[32m      7\u001b[39m \u001b[38;5;28mprint\u001b[39m(op.info())\n\u001b[32m      8\u001b[39m \u001b[38;5;66;03m# save op to parquet file in output_destination\u001b[39;00m\n\u001b[32m      9\u001b[39m \u001b[38;5;66;03m# output_destination = '/projects/ods/us_eds/users/ai_hub/tmp/openalex_data_with_bikg.parquet'\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/projects/ods/us_eds/users/ai_hub/anaconda3/envs/ama_genai/lib/python3.13/site-packages/dask/dataframe/dask_expr/_collection.py:389\u001b[39m, in \u001b[36mFrameBase.__len__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    388\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__len__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m389\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mnew_collection\u001b[49m\u001b[43m(\u001b[49m\u001b[43mLen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompute\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/projects/ods/us_eds/users/ai_hub/anaconda3/envs/ama_genai/lib/python3.13/site-packages/dask/dataframe/dask_expr/_collection.py:491\u001b[39m, in \u001b[36mFrameBase.compute\u001b[39m\u001b[34m(self, fuse, concatenate, **kwargs)\u001b[39m\n\u001b[32m    489\u001b[39m     out = out.repartition(npartitions=\u001b[32m1\u001b[39m)\n\u001b[32m    490\u001b[39m out = out.optimize(fuse=fuse)\n\u001b[32m--> \u001b[39m\u001b[32m491\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mDaskMethodsMixin\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/projects/ods/us_eds/users/ai_hub/anaconda3/envs/ama_genai/lib/python3.13/site-packages/dask/base.py:370\u001b[39m, in \u001b[36mDaskMethodsMixin.compute\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m    346\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcompute\u001b[39m(\u001b[38;5;28mself\u001b[39m, **kwargs):\n\u001b[32m    347\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Compute this dask collection\u001b[39;00m\n\u001b[32m    348\u001b[39m \n\u001b[32m    349\u001b[39m \u001b[33;03m    This turns a lazy Dask collection into its in-memory equivalent.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    368\u001b[39m \u001b[33;03m    dask.compute\u001b[39;00m\n\u001b[32m    369\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m370\u001b[39m     (result,) = \u001b[43mcompute\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtraverse\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    371\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32m/projects/ods/us_eds/users/ai_hub/anaconda3/envs/ama_genai/lib/python3.13/site-packages/dask/base.py:656\u001b[39m, in \u001b[36mcompute\u001b[39m\u001b[34m(traverse, optimize_graph, scheduler, get, *args, **kwargs)\u001b[39m\n\u001b[32m    653\u001b[39m     postcomputes.append(x.__dask_postcompute__())\n\u001b[32m    655\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m shorten_traceback():\n\u001b[32m--> \u001b[39m\u001b[32m656\u001b[39m     results = \u001b[43mschedule\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdsk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeys\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    658\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m repack([f(r, *a) \u001b[38;5;28;01mfor\u001b[39;00m r, (f, a) \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(results, postcomputes)])\n", "\u001b[36mFile \u001b[39m\u001b[32m/projects/ods/us_eds/users/ai_hub/anaconda3/envs/ama_genai/lib/python3.13/queue.py:202\u001b[39m, in \u001b[36mQueue.get\u001b[39m\u001b[34m(self, block, timeout)\u001b[39m\n\u001b[32m    200\u001b[39m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    201\u001b[39m     \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._qsize():\n\u001b[32m--> \u001b[39m\u001b[32m202\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mnot_empty\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwait\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    203\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.is_shutdown \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._qsize():\n\u001b[32m    204\u001b[39m             \u001b[38;5;28;01mraise\u001b[39;00m ShutDown\n", "\u001b[36mFile \u001b[39m\u001b[32m/projects/ods/us_eds/users/ai_hub/anaconda3/envs/ama_genai/lib/python3.13/threading.py:359\u001b[39m, in \u001b[36mCondition.wait\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    357\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:    \u001b[38;5;66;03m# restore state no matter what (e.g., KeyboardInterrupt)\u001b[39;00m\n\u001b[32m    358\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m359\u001b[39m         \u001b[43mwaiter\u001b[49m\u001b[43m.\u001b[49m\u001b[43macquire\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    360\u001b[39m         gotit = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    361\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["from dask.diagnostics import ProgressBar\n", "with ProgressBar():\n", "    loader = BikgDataLoader()\n", "    op = loader.add_bikg_data()\n", "    #25299\n", "    print(len(op))\n", "    print(op.info())\n", "    # save op to parquet file in output_destination\n", "    # output_destination = '/projects/ods/us_eds/users/ai_hub/tmp/openalex_data_with_bikg.parquet'\n", "    output_destination = './output.parquet'\n", "    op.to_parquet(output_destination, overwrite=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[                                        ] | 0% Completed | 218.40 ms"]}, {"name": "stdout", "output_type": "stream", "text": ["[########################################] | 100% Completed | 9.03 ss\n", "45282318\n", "[########################################] | 100% Completed | 9.14 ss\n", "0\n"]}], "source": ["doc_parquet_file = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_doc_index'\n", "df_doc = dd.read_parquet(doc_parquet_file)\n", "with ProgressBar():\n", "    print(len(df_doc[df_doc['is_genai_inference'].astype(str) == 'True']))\n", "    print(len(df_doc[df_doc['is_genai_inference'].astype(str) != 'True']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[########################################] | 100% Completed | 101.03 ms\n", "5205\n"]}], "source": ["df1 = dd.read_parquet('zs_oa_adc_curated_annotated_with_content_2025.03-02.parquet')\n", "with ProgressBar():\n", "    print(len(df1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[########################################] | 100% Completed | 101.27 ms\n", "15037\n"]}], "source": ["df2 = dd.read_parquet('zs_oa_adc_curated_annotated_with_content_2025.03-03.parquet')\n", "with ProgressBar():\n", "    print(len(df2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_parquet_file = '/projects/it/public/bikg/downloads/2025-04-03-5.4.4/nlp_doc_index'\n", "df_doc = dd.read_parquet(doc_parquet_file)\n", "conn = sqlite3.connect(db_file)\n", "df_db = pd.read_sql_query(\"select * from dim_openalex_works;\", conn)\n", "id_list = df_db['id'].tolist()\n", "input = [id.split('/')[-1] for id in id_list]\n", "conn.close()\n", "df_doc = df_doc[df_doc['oa_works_id'].isin(input)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[########################################] | 100% Completed | 4.42 ss\n", "15155\n"]}], "source": ["with ProgressBar():\n", "    print(len(df_doc))"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['W2088226047', 'W2323534437', 'W2891589486', 'W2998012982', 'W2116335144', 'W2071019150', 'W1980910331', 'W4391757646', 'W3191004831', 'W2092902276', 'W2050222325', 'W2324411273', 'W2976352116', 'W2291108238', 'W3198694797', 'W2129429514', 'W1524815067', 'W1604064808', 'W2991954871', 'W2024651553', 'W2086870979', 'W2462429420', 'W1971289426', 'W3121053691', 'W2072624401', 'W2046138183', 'W1991497659', 'W2032235065', 'W2763492611', 'W1916436059', 'W4389454611', 'W2975888204', 'W2050100708', 'W2110164810', 'W2063516590', 'W1964010927', 'W4319320281', 'W1975614861', 'W1606436237', 'W2006351191', 'W2040855010', 'W2037982474', 'W2018453465', 'W2020929927', 'W2016700351', 'W1992553456', 'W2023820230', 'W2025922969', 'W2904238911', 'W1985226621', 'W4229663250', 'W2432742889', 'W2896099289', 'W2970461004', 'W2053435593', 'W2057089927', 'W1642873092', 'W2074364056', 'W2025140437', 'W4238256244', 'W1994126393', 'W2325786224', 'W3153990830', 'W2898174432', 'W2897652969', 'W1767380958', 'W4383218370', 'W2007917384', 'W1593725251', 'W2116150122', 'W2322090980', 'W2093580676', 'W1984548348', 'W2139934531', 'W3181129406', 'W1984251892', 'W2052545791', 'W2088788502', 'W2076905116', 'W2475297214', 'W1569560733', 'W2921099873', 'W1974128502', 'W1582760249', 'W2084739125', 'W1971741240', 'W2938889495', 'W2567320397', 'W2028069487', 'W1574983751', 'W2598830924', 'W2928432651', 'W2042844150', 'W4252911605', 'W2033111644', 'W2035064666', 'W1533229981', 'W1624401920', 'W2726008343', 'W2345470430', 'W4379986466', 'W3040270254', 'W1997284206', 'W2017759923', 'W2751416367', 'W2005205763', 'W1997400948', 'W4251824239', 'W2789665281', 'W1968949976', 'W2014225194', 'W2040897181', 'W2045448921', 'W2072183005', 'W1988241631', 'W4385879053', 'W2858831', 'W2101664092', 'W2334093997', 'W1619934129', 'W2765782352', 'W2670390373', 'W2005675755', 'W2088536552', 'W2016828297', 'W2149673010', 'W2006047932', 'W1643888758', 'W2801900149', 'W4247130905', 'W2293120410', 'W2736141278', 'W2075918337', 'W2050666566', 'W2625042926', 'W2323331803', 'W2045930308', 'W2077521424', 'W1972421519', 'W2570218443', 'W1965981994', 'W4292488030', 'W1981601459', 'W2588053707', 'W1966634965', 'W2800725871', 'W2138795497', 'W4390673654', 'W2160466843', 'W4213185425', 'W2566753678', 'W2034971573', 'W1521126454', 'W1510613202', 'W2961969123', 'W4294576509', 'W2016103085', 'W2763367287', 'W4238404949', 'W2178103997', 'W2792432907', 'W4230873105', 'W2603386525', 'W1973251492', 'W1529151652', 'W1974798932', 'W2089342770', 'W2320028767', 'W2345520280', 'W1978932179', 'W2315120420', 'W4213018015', 'W1978452077', 'W4319298155', 'W2083483697', 'W1243058577', 'W2988773550', 'W2062395662', 'W2072812577', 'W2331609069', 'W2090057677', 'W2920596526', 'W4405738102', 'W1987863398', 'W2039359477', 'W2530303858', 'W1980100889', 'W2083331687', 'W2022301231', 'W2035166160', 'W2022652983', 'W1993870540', 'W2917928050', 'W2167899780', 'W4392984612', 'W4392984612', 'W2008489200', 'W2945635306', 'W2314659450', 'W2953427423', 'W3028293850', 'W1992175481', 'W2377535456', 'W2067839517']\n"]}], "source": ["wants = df2[~df2['is_genai_inference']]['oa_works_id'].tolist()\n", "print(wants)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["def get_section(sections, section):\n", "    ret = []\n", "    for record in sections:\n", "        if(record.get(\"section\")==section):\n", "            ret.append(record.get(\"text\"))\n", "\n", "    return ret"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Typical acute promyelocytic leukemia (APL) is associated with the t(15;17) translocation, expression of a PML/RARA fusion transcript, and responsiveness to all-transretinoic acid (ATRA). Rare APL cases implicating the RARA but not the PMLgene have been reported. Cases with t(11;17)(q23;q21) which fuses the PLZF and RARA genes do not respond to ATRA. In contrast, cases with t(11;17)(q13;q21) and t(5;17)(q35;q21) which fuse RARA with NuMA and NPM, respectively, were reported to be sensitive to ATRA. We described previously an APL case with an unbalanced t(5;17) implicating RARA but neither PML nor PLZF. Here, we show that in this case: (1) the NPM gene is not involved, as demonstrated by RT-PCR and Southern blot; (2) response to ATRA in vitro is atypical, as demonstrated by morphological and functional maturation assays; and (3) PML nuclear bodies are not disrupted, as evidenced by immunofluorescence staining.']\n"]}], "source": ["want = 'W2323534437'\n", "# print unbounded witdth for column\n", "pd.set_option('display.max_colwidth', None)\n", "sections = df2[df2['oa_works_id'] == want]['doc_sections'].iloc[0]\n", "#sections is a list of dicts, parse it to python object\n", "# sections = ast.literal_eval(sections)\n", "text1 = get_section(sections, \"abstract\")\n", "print(text1)"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Typical acute promyelocytic leukemia (APL) is associated with the t(15;17) translocation, expression of a PML/RARA fusion transcript, and responsiveness to all-transretinoic acid (ATRA).']\n"]}], "source": ["\n", "ours = pd.read_parquet('/projects/ods/us_eds/users/ai_hub/tmp/output.parquet')\n", "# with ProgressBar():\n", "sections = ours[ours['oa_works_id'] == want]['doc_sections'].iloc[0]\n", "text2 = get_section(sections, \"abstract\")\n", "print(text2)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["for want in wants:\n", "    sections = df2[df2['oa_works_id'] == want]['doc_sections'].iloc[0]\n", "    body1 = get_section(sections, \"body\")\n", "    abstract1 = get_section(sections, \"abstract\")\n", "    sections = ours[ours['oa_works_id'] == want]['doc_sections']\n", "    text2 = get_section(sections, \"body\")"]}], "metadata": {"kernelspec": {"display_name": "ama_genai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}